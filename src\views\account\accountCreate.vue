<script setup>
import { computed, ref, onMounted, watch, onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import {
  addaccadmin,
  editaccInfo,
  getaccInfo,
  roleList,
  verifyPhone,
  getPhonecode,
  getNonCancell
} from "@/api/leaderLecturer.js";
import { getBindCode, unbindWxCode } from "@/api/user.js";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { formatTime } from "@/utils/index";
import {
  compareObjects,
  debounce,
  removeEmptyValues,
  isEmpty
} from "@iceywu/utils";
import qrcode from "qrcode";
import { organizationFindById, verifyUsername } from "@/api/institution";
import { pinyin } from "pinyin-pro";
import { Hide, Message, View, Loading } from "@element-plus/icons-vue";
import { courseStore } from "@/store/modules/course.js";
import { tr } from "element-plus/es/locale/index.mjs";
import WxQrCode from "@/components/WxQrCode/index.vue";
import { useTripStore } from "@/store/modules/trip.js";
const props = defineProps({
  courseRoleId: {
    type: Number
  }
});
const tripStore = useTripStore();
const courseStoreInfo = courseStore();
const router = useRouter();
const route = useRoute();
const url = ref("https:/www.baidu.com/");
const qrCodeData = ref("");
const inalias = ref("");
const getiphorgId = ref("");
const alias = ref("");
const richFlag = ref(false);
const isCaptchaLoading = ref(false);
const isCaptchaDisabled = ref(false);

// 微信二维码组件
const wxQrCodeRef = ref(null);
const unbindWxLoading = ref(false);
const showUnbindDialog = ref(false);

// 处理微信回调
const handleWxCallback = (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }

  const storedState = sessionStorage.getItem("wx_login_state");

  // 状态码 是否一致
  if (state !== storedState) {
    ElMessage.error("微信状态不一致，请重新扫码");
    isWxCallbackProcessed.value = true; // 标记为已处理
    return;
  }

  console.log(form.value.isBingWx, "isbind");

  // 状态码一致，处理微信绑定
  if (form.value.isBindWx) {
    // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
    ElMessage.info("当前账号已绑定微信");
    isWxCallbackProcessed.value = true; // 标记为已处理
  } else {
    // 如果当前未绑定微信，处理绑定逻辑
    bindCode(code, processedKey);
  }
};

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query.id,
    userType: "ORGANIZATION_ADMIN"
  };

  try {
    const res = await getBindCode(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${form.value.account}@${inalias.value}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");

      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");

      // 重新获取用户信息
      await getacctable();

      // 重置二维码组件状态
      if (wxQrCodeRef.value) {
        wxQrCodeRef.value.resetInit();
      }

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
    throw new Error(err);
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: route.query.id,
        userType: "ORGANIZATION_ADMIN"
      };

      const res = await unbindWxCode(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号“${form.value.account}@${inalias.value}”完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage.success("微信解绑成功");

        // 关闭对话框
        showUnbindDialog.value = false;

        await getacctable();
      }
      console.log("微信解绑完成");
    } catch (error) {
      ElMessage.error("微信解绑失败，请重试");
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 清理路由参数
const resetToInitialState = () => {
  // 清理路由查询参数，保持当前路径
  // router.replace({
  //   path: route.path,
  //   query: {
  //     // 保留必要的查询参数
  //     id: route.query.id,
  //     title: route.query.title
  //     // 移除微信登录相关的参数
  //     // code, state, path 将被清除
  //   }
  // });
};

onBeforeMount(() => {
  if (props.courseRoleId || route.query.courseRoleId) {
    formData.value = [
      {
        label: "姓名",
        type: "input",
        prop: "name",
        check: true,
        placeholder: "请输入姓名",
        width: "400px",
        maxLength: 10,
        inline: true
      },
      {
        label: "账号",
        type: "input",
        prop: "account",
        check: true,
        placeholder: "请输入账号",
        width: "400px",
        maxLength: 20,
        inline: true
      },
      {
        label: "手机号",
        type: "input",
        prop: "phone",
        check: true,
        isView: true,
        placeholder: "请输入手机号",
        width: "400px",
        buttonText: "获取验证码",
        maxLength: 11,
        inline: true
      },
      // {
      //   label: "验证码",
      //   type: "input",
      //   prop: "codenumb",
      //   placeholder: "请输入验证码",
      //   width: "400px",
      //   // check: true,
      //   hasButton: true,
      //   buttonText: "获取验证码"
      // },
      {
        label: "邮箱",
        type: "input",
        prop: "email",
        placeholder: "请输入邮箱",
        width: "400px",
        maxLength: 30,
        inline: true
      },
      {
        label: "身份证号",
        type: "input",
        prop: "idNumber",
        isView: true,
        placeholder: "请输入身份证号",
        width: "400px",
        maxLength: 18,
        inline: true
      },
      {
        label: "微信绑定",
        type: "img",
        prop: "isBindWx",
        url: "",
        width: "400px",
        height: "120px",
        inline: true
      }
    ];
  } else {
    formData.value = [
      {
        label: "姓名",
        type: "input",
        prop: "name",
        check: true,
        placeholder: "请输入姓名",
        width: "400px",
        maxLength: 10,
        inline: true
      },
      {
        label: "账号",
        type: "input",
        prop: "account",
        check: true,
        placeholder: "请输入账号",
        width: "400px",
        maxLength: 20,
        inline: true
      },
      {
        label: "手机号",
        type: "input",
        prop: "phone",
        check: true,
        isView: true,
        placeholder: "请输入手机号",
        width: "400px",
        buttonText: "获取验证码",
        maxLength: 11,
        inline: true
      },
      // {
      //   label: "验证码",
      //   type: "input",
      //   prop: "codenumb",
      //   placeholder: "请输入验证码",
      //   width: "400px",
      //   // check: true,
      //   hasButton: true,
      //   buttonText: "获取验证码"
      // },
      {
        label: "邮箱",
        type: "input",
        prop: "email",
        placeholder: "请输入邮箱",
        width: "400px",
        maxLength: 30,
        inline: true
      },
      {
        label: "身份证号",
        type: "input",
        prop: "idNumber",
        isView: true,
        placeholder: "请输入身份证号",
        width: "400px",
        maxLength: 18,
        inline: true
      },
      {
        label: "角色",
        type: "checkbox",
        prop: "roleIds",
        width: "400px",
        list: [],
        inline: true
      },
      {
        label: "微信绑定",
        type: "img",
        prop: "isBindWx",
        url: "",
        width: "400px",
        height: "120px",
        inline: true
      }
    ];
  }
});

onMounted(() => {
  initRoleData();
  if (istitle.value) {
    getacctable(); //回显详情
  } else {
    richFlag.value = true;
  }
  getroleList(); //获取角色列表
  getTableList(); //获取机构别名
  qrcode.toDataURL(url.value, (err, url) => {
    if (err) {
      console.error(err);
    } else {
      qrCodeData.value = url;
    }
  });

  console.log(redirectPathWithQuery.value, "跳转的路由加参数");
});
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "账号ID",
    value: "",
    key: "id"
  },
  {
    id: "2",
    label: "创建时间",
    value: "",
    key: "createdAt"
  }
]);
// 表单
const form = ref({
  name: "", //姓名
  account: "", //账号
  phone: "", //手机号码
  // organizationId: 1, //机构ID
  email: null, //邮箱
  idNumber: "", //身份号
  roleIds: [],
  isBindWx: null
});
const formRef = ref(null);
const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    maxLength: 10,
    inline: true
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    maxLength: 20,
    inline: true
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    isView: true,
    placeholder: "请输入手机号",
    width: "400px",
    buttonText: "获取验证码",
    maxLength: 11,
    inline: true
  },
  // {
  //   label: "验证码",
  //   type: "input",
  //   prop: "codenumb",
  //   placeholder: "请输入验证码",
  //   width: "400px",
  //   // check: true,
  //   hasButton: true,
  //   buttonText: "获取验证码"
  // },
  {
    label: "邮箱",
    type: "input",
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    maxLength: 30,
    inline: true
  },
  {
    label: "身份证号",
    type: "input",
    prop: "idNumber",
    isView: true,
    placeholder: "请输入身份证号",
    width: "400px",
    maxLength: 18,
    inline: true
  },
  {
    label: "角色",
    type: "checkbox",
    prop: "roleIds",
    width: "400px",
    list: [],
    inline: true
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    url: "",
    width: "400px",
    height: "120px",
    inline: true
  }
]);
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (
    oldData.value.phoneCt &&
    (value == oldData.value.phone || value == decrypt(oldData.value.phoneCt))
  ) {
    if (formData.value[3].label === "验证码") {
      formData.value[2].hasButton = false;
      formData.value.splice(3, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  // return;
  if (!value) {
    isCaptchaDisabled.value = true;
    return callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    isCaptchaDisabled.value = true;
    return callback(new Error("请输入有效的手机号码"));
  } else {
    isCaptchaDisabled.value = false;
    // const oldPhone = oldData.value?.phone;
    // const decryptedPhone = oldPhone ? decrypt(oldPhone) : null;
    // if (value == decryptedPhone) {
    //   return callback();
    // }
    if (formData.value[3].label !== "验证码") {
      formData.value[2].hasButton = false;
      // 往数组指定位置添加验证码字段
      formData.value.splice(3, 0, {
        label: "验证码",
        type: "input",
        prop: "code",
        span: 1,
        placeholder: "请输入验证码",
        width: "400px",
        check: true
      });

      // 如果form中没有code字段，初始化它
      if (!form.value.code) {
        form.value.code = "";
      }
    }
    if (value == decrypt(oldData.value.phoneCt)) {
      callback();
    } else {
      const params = {
        phone: encryption(value),
        organizationId: getiphorgId.value
      };
      try {
        const response = await verifyPhone(params);
        // console.log("🌈-----response-----", response);
        if (response.code === 70008) {
          isCaptchaDisabled.value = true;
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }

    // callback();
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (value === oldData.value.account) {
    callback();
    return;
  }
  const accountPattern = /^(adm[in]{1,2}|root|sysadm|[\w-]*(admin)[\w-]*)$/;
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        // callback();
        if (accountPattern.test(value)) {
          callback(new Error("该用户名不可用，请重新输入"));
        } else {
          callback();
        }
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
const ID_NUMBER_REGEX = /(^\d{15}$)|(^\d{17}([0-9X])$)/i;
const CHECK_CODES = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
const FACTORS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
const validateIdNumber = (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  if (
    oldData.value.idNumberCt &&
    (value == oldData.value.idNumber ||
      value == decrypt(oldData.value.idNumberCt))
  ) {
    callback();
    return;
  }
  if (!ID_NUMBER_REGEX.test(value)) {
    return callback(new Error("身份证号格式不正确"));
  }
  if (value.length === 18 && !isValidChineseIDChecksum(value)) {
    return callback(new Error("身份证号不正确"));
  }
  callback();
};
function isValidChineseIDChecksum(idNumber) {
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idNumber[i], 10);
    if (isNaN(digit)) {
      return false; // 遇到非数字字符返回 false
    }
    sum += digit * FACTORS[i];
  }

  const index = sum % 11;
  const expectedCheckCode = CHECK_CODES[index];
  return expectedCheckCode.toUpperCase() === idNumber[17].toUpperCase();
}
//身份证输入筛选
const filterChineseInput = (value, prop) => {
  const numberWhite = ["phone", "idNumber"];
  if (numberWhite.includes(prop)) {
    const filteredValue = value.replace(/[^\dX]/gi, "");
    form.value[prop] = filteredValue.toUpperCase();
  } else {
    form.value[prop] = value.replace(/\s/g, "");
  }
};
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "姓名不能为空", trigger: "blur" },
    { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [
    // { required: true, message: "手机号不能为空", trigger: "blur" },
    // { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
    { required: true, validator: validatePhoneNumber, trigger: "blur" }
  ],
  code: [{ required: true, message: "验证码不能为空", trigger: "blur" }],
  idNumber: [{ validator: validateIdNumber, trigger: "blur" }],
  email: [
    {
      validator: (rule, value, callBack) => {
        if (!value) {
          callBack();
          return;
        }
        // 邮箱正则表达式
        const pattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

        // 测试邮箱是否匹配模式
        if (!pattern.test(value)) {
          callBack("邮箱格式错误");
          return;
        }
        callBack();
      },
      trigger: "blur"
    }
  ]
  // codenumb: [{ required: true, message: "验证码不能为空", trigger: "blur" }]
});

// 提交表单/编辑
const oldData = ref({});
const submitLoading = ref(false);
const submitForm = debounce(
  async () => {
    formRef.value.validate(async (s, b) => {
      if (s) {
        if (submitLoading.value) return;
        submitLoading.value = true;
        // console.log("表单数据:", form.value);
        // 新增
        const addParam = {
          name: form.value?.name,
          // account: form.value?.account,
          // account: `${form.value?.account}@${inalias.value}`,
          account: `${form.value?.account}@${inalias.value}`,
          phone: encryption(form.value?.phone || ""),
          // organizationId: form.value?.organizationId,
          email: form.value?.email,
          idNumber: encryption(form.value.idNumber || ""),
          roleIds: form.value.roleIds,
          code: form.value?.code,
          isAdmin: false
        };
        // 编辑
        // const editParam = {
        //   id: form.value?.id,
        //   account: form.value?.account || "",
        //   name: form.value.name || "",
        //   phone: form.value.phone || "",
        //   idNumber: form.value.idNumber || "",
        //   email: form.value.email || ""
        // };
        console.log("🦄-----oldData.value-----", oldData.value);
        console.log("🍭-----addParam-----", removeEmptyValues(addParam));
        let paramsData = {};
        for (const paramsDataKey in form.value) {
          console.log("🐬-----paramsDataKey-----", addParam[paramsDataKey]);
          let isArray = Array.isArray(addParam[paramsDataKey]);
          if (isArray) {
            if (addParam[paramsDataKey].length > 0) {
              paramsData[paramsDataKey] = addParam[paramsDataKey];
            }
          } else {
            if (addParam[paramsDataKey]) {
              paramsData[paramsDataKey] = addParam[paramsDataKey];
            }
          }
        }
        let params = compareObjects(copyData.value, paramsData);
        // 判断手机号是否修改;
        // 没修改
        if (
          form.value.phone === oldData.value.phone ||
          form.value.phone === decrypt(oldData.value.phoneCt)
        ) {
          delete params.phone;
        }
        // 已修改
        if (paramsData.phone) {
          // 判断手机号是否已经是掩码状态
          const phoneIndex = formData.value.findIndex(
            item => item.prop === "phone"
          );

          if (phoneIndex !== -1 && formData.value[phoneIndex].isView) {
            // 如果是掩码状态，使用原始的加密数据
            if (oldData.value.phoneCt) {
              paramsData.phone = oldData.value.phoneCt;
            } else {
              paramsData.phone = encryption(paramsData.phone);
            }
          } else if (form.value.phone === decrypt(oldData.value.phoneCt)) {
            // 如果是已解密但未修改，使用原始加密数据
            paramsData.phone = oldData.value.phoneCt;
          } else {
            // 否则加密新输入的手机号
            paramsData.phone = encryption(paramsData.phone);
          }
        }
        params.id = form.value?.id;
        if (route.query.roleId === "2" || props.courseRoleId === 2) {
          addParam.roleIds = [2];
        } else if (route.query.roleId === "3" || props.courseRoleId === 3) {
          addParam.roleIds = [3];
        }
        // console.log("🌈-----params-----", params);
        // console.log("🍧-----addParam-----", addParam);
        // console.log("🌳-----paramsData-----", paramsData);
        // return;
        // 编辑成功
        const operateLog = {
          operateLogType: "ACCOUNT_MANAGEMENT",
          operateType: istitle.value
            ? `编辑了${form.value.name}的账号`
            : `创建了${form.value.name}的账号`
        };

        let tempInitialPayload;
        if (istitle.value) {
          tempInitialPayload = { ...params };
          tempInitialPayload.id = form.value?.id;
        } else {
          tempInitialPayload = { ...addParam };
        }

        let payloadToSend = removeEmptyValues(tempInitialPayload);

        if (istitle.value) {
          payloadToSend.id = form.value?.id;
        }

        // 处理email - 有值就保留，无值就删除
        if (form.value.email && form.value.email.trim() !== "") {
          payloadToSend.email = form.value.email;
        } else {
          delete payloadToSend.email;
        }

        // 处理身份证号 - 根据当前状态判断如何处理
        if (form.value.idNumber && form.value.idNumber.trim() !== "") {
          // 判断身份证号是否已经是掩码状态
          const idNumberIndex = formData.value.findIndex(
            item => item.prop === "idNumber"
          );

          if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
            // 如果是掩码状态，使用原始的加密数据
            if (oldData.value.idNumberCt) {
              payloadToSend.idNumber = oldData.value.idNumberCt;
            }
          } else if (form.value.idNumber === oldData.value.idNumberDecrypted) {
            // 如果显示的是解密形式且值未变，使用原始的加密数据
            if (oldData.value.idNumberCt) {
              payloadToSend.idNumber = oldData.value.idNumberCt;
            }
          } else {
            // 如果是新输入的数据，进行加密
            payloadToSend.idNumber = encryption(form.value.idNumber);
          }
        } else {
          delete payloadToSend.idNumber;
        }

        let requestApi = istitle.value
          ? editaccInfo(payloadToSend, operateLog)
          : addaccadmin(payloadToSend, operateLog);
        // delete addParam.phone; // This line's original purpose is maintained
        const { code, msg, data } = await requestApi;
        // console.log("🌵---1--result-----", res);
        if (code == 200) {
          submitLoading.value = false;
          if (istitle.value) {
            ElMessage({
              message: "编辑成功",
              type: "success"
            });
            router.push({ path: "/account/accountManage" });
          } else {
            ElMessage({
              message: "创建成功",
              type: "success"
            });
            const currentPath = router.currentRoute.value.path;

            // 合并新创建的角色和原有角色，而不是替换
            if (route.query.roleId === "2" || props.courseRoleId === 2) {
              // 获取当前store中已有的讲师角色
              const existingLecturers = courseStoreInfo.lecturerInfo || [];
              // 创建一个Map用于去重
              const lecturerMap = new Map();

              // 先添加已有角色
              existingLecturers.forEach(lecturer => {
                lecturerMap.set(lecturer.id, lecturer);
              });

              // 再添加新角色
              lecturerMap.set(data.id, { id: data.id, name: data.name });

              // 转换回数组并保存
              const mergedLecturers = Array.from(lecturerMap.values());
              courseStoreInfo.saveLecturerInfo(mergedLecturers);
              if (!isEmpty(route.query.lecturerIndex)) {
                tripStore.saveLecturerData({
                  id: data.id,
                  name: data.name
                });
              }
            } else if (route.query.roleId === "3" || props.courseRoleId === 3) {
              // 获取当前store中已有的领队角色
              const existingLeaders = courseStoreInfo.leaderInfo || [];
              // 创建一个Map用于去重
              const leaderMap = new Map();

              // 先添加已有角色
              existingLeaders.forEach(leader => {
                leaderMap.set(leader.id, leader);
              });

              // 再添加新角色
              leaderMap.set(data.id, { id: data.id, name: data.name });

              // 转换回数组并保存
              const mergedLeaders = Array.from(leaderMap.values());
              courseStoreInfo.saveLeaderInfo(mergedLeaders);
            }
            let createName = "";
            if (!isEmpty(route.query.lecturerIndex)) {
              createName = "createTrip";
            } else {
              createName = "create";
            }
            if (
              route.query.type === "create" ||
              route.query.type === "draft" ||
              route.query?.type === "createPeriod"
            ) {
              router.replace({
                path: "/course/courseCreate",
                query: {
                  type: route.query.type,
                  copyId: route.query.copyId,
                  courseId: route.query.courseId,
                  id: route.query.id,
                  create: createName,
                  draftId: route.query.draftId,
                  lecturerIndex: route.query.lecturerIndex
                }
              });
            } else if (route.query.type === "edite") {
              router.replace({
                path: "/course/coursePeriodEdite",
                query: {
                  type: route.query.type,
                  periodId: route.query.periodId,
                  courseId: route.query.courseId,
                  fromPage: route.query.fromPage,
                  create: createName,
                  lecturerIndex: route.query.lecturerIndex
                }
              });
            } else if (route.query.periodId) {
              router.replace({
                path: "/course/periodEdite",
                query: {
                  periodId: route.query.periodId,
                  courseId: route.query.courseId,
                  create: createName,
                  lecturerIndex: route.query.lecturerIndex
                }
              });
            } else if (currentPath.includes("/account/teamCreate")) {
              // 从领队创建页面来的，返回领队创建页面
              router.push({
                path: "/account/teamManage"
              });
            } else if (currentPath.includes("/account/teacherCreate")) {
              // 从讲师创建页面来的，返回讲师创建页面
              router.push({
                path: "/account/teacherManage"
              });
            } else {
              router.push({ path: "/account/accountManage" });
            }
          }
        } else {
          submitLoading.value = false;
          ElMessage({
            message: msg,
            type: "error"
          });
        }
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);

// 账号详情回显
// const getacctable = async () => {
//   const [err, result] = await requestTo(getaccInfo({ id: route.query?.id }));
//   // console.log("🎉---1--result-----", result);
//   if (result) {
//     tableHeader.value = tableHeader.value.map(item => ({
//       ...item,
//       value:
//         item.key === "createdAt"
//           ? formatTime(result[item.key], "YYYY-MM-DD HH:mm")
//           : (result[item.key] ?? "--")
//     }));
//     form.value = {
//       id: result?.id,
//       name: result?.name,
//       account: result?.account,
//       phone: decrypt(result?.phoneCt),
//       email: result?.email,
//       idNumber: result?.idNumberCt ? decrypt(result?.idNumberCt) : ""
//       // roleIds: result?.roleIds
//       // email: result?.email
//     };
//     // console.log(
//     //   "🎉-----result.roles-----",
//     // );
//     // 确保 roleIds 对应的选项回显
//     // const roleField = formData.value.find(item => item.prop === "roleIds");
//     // if (roleField) {
//     //   roleField.list.forEach(role => {
//     //     role.checked = result.roleIds.includes(role.id);
//     //   });
//     // }
//   }
//   oldData.value = {
//     isAdmin: false,
//     name: result?.name,
//     account: result?.account,
//     email: result?.email,
//     phone: result?.phoneCt,
//     idNumber: result?.idNumberCt
//     // roleIds: result?.roleIds
//   };
//   // console.log(decrypt(result?.phoneCt));
// };
const copyData = ref({});

const getacctable = async () => {
  const [err, result] = await requestTo(getaccInfo({ id: route.query?.id }));
  if (result) {
    tableHeader.value = tableHeader.value.map(item => ({
      ...item,
      value:
        item.key === "createdAt"
          ? formatTime(result[item.key], "YYYY-MM-DD HH:mm")
          : (result[item.key] ?? "--")
    }));

    // 解密身份证号，用于表单显示
    const decryptedIdNumber = result?.idNumberCt
      ? decrypt(result?.idNumberCt)
      : "";

    form.value = {
      id: result?.id,
      name: result?.name,
      account: result?.account.split("@")[0],
      phone: result?.phone,
      email: result?.email,
      idNumber: result?.idNumber, // 初始使用掩码形式
      roleIds: result.roles?.map(role => role.id) || [],
      isBindWx: result?.isBindWx
    };

    const roleField = formData.value.find(item => item.prop === "roleIds");
    if (roleField) {
      roleField.list.forEach(role => {
        role.checked = form.value.roleIds.includes(role.id);
      });
    }
  }
  richFlag.value = true;

  oldData.value = {
    isAdmin: false,
    name: result?.name,
    account: result?.account,
    email: result?.email,
    phone: result?.phone,
    phoneCt: result?.phoneCt,
    idNumber: result?.idNumber, // 掩码形式的身份证号
    idNumberCt: result?.idNumberCt, // 加密形式的身份证号
    idNumberDecrypted: result?.idNumberCt ? decrypt(result?.idNumberCt) : "" // 解密后的身份证号
  };

  copyData.value = {
    name: result?.name,
    account: result?.account,
    phone: encryption(result?.phone || ""),
    email: result?.email,
    idNumber: result?.idNumberCt || encryption(result?.idNumber || ""),
    leaderLecturerType: route.query?.title === "jsMang" ? "LECTURER" : "LEADER"
  };
};

// 获取验证码
const getCaptcha = async phoneCode => {
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!phoneCode) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  if (!phoneRegex.test(phoneCode)) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "电话号码格式不正确", type: "warning" });
    return;
  }
  const params = {
    phone: encryption(phoneCode),
    codeType: "VERIFICATION_CODE"
  };
  isCaptchaDisabled.value = false;
  // return;
  const { code, msg } = await getPhonecode(params);
  if (code == 200) {
    ElMessage({
      message: "验证码已发送",
      type: "success"
    });
  }

  console.log("🐳-----params-----", params);
};
// 获取角色列表(不分页)
const getroleList = async (disabledIds = []) => {
  const [err, result] = await requestTo(roleList());
  if (!err && result) {
    // 处理数据
    const roleList = result.map(role => ({
      id: role.id,
      list_name: role.name,
      disabled: disabledIds.includes(role.id)
    }));
    const roleField = formData.value.find(item => item.prop === "roleIds");
    if (roleField) {
      roleField.list = roleList;
    }
  }
};
console.log("🐳-----route.query-----", route.query);
// 取消
const cancelForm = () => {
  const currentPath = router.currentRoute.value.path;
  tripStore.saveLecturerData({});
  let createName = "";
  if (!isEmpty(route.query.lecturerIndex)) {
    createName = "createTrip";
  } else {
    createName = "create";
  }
  // 点击取消按钮时不修改store中已有的数据
  // 只需要返回到之前的页面即可
  if (currentPath.includes("periodCopy/create")) {
    // 从复制课期页面来的课期创建页面
    router.push({
      path: "/course/periodCopy/create",
      query: {
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        type: "copy",
        create: "create" // 添加create参数，保持与确认选择相同的路由参数
      }
    });
  } else if (
    route.query.type === "create" ||
    route.query.type === "draft" ||
    route.query?.type === "createPeriod"
  ) {
    router.replace({
      path: "/course/courseCreate",
      query: {
        type: route.query.type,
        copyId: route.query.copyId,
        courseId: route.query.courseId,
        id: route.query.id,
        create: createName,
        draftId: route.query.draftId,
        lecturerIndex: route.query.lecturerIndex
      }
    });
  } else if (route.query.type === "edite") {
    router.replace({
      path: "/course/coursePeriodEdite",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        create: createName,
        lecturerIndex: route.query.lecturerIndex
      }
    });
  } else if (currentPath.includes("/account/teamCreate")) {
    // 从领队创建页面来的，返回领队创建页面
    router.push({
      path: "/account/teamManage"
    });
  } else if (currentPath.includes("/account/teacherCreate")) {
    // 从讲师创建页面来的，返回讲师创建页面
    router.push({
      path: "/account/teacherManage"
    });
  } else {
    // 默认返回账号管理页面
    router.push({
      path: "/account/accountManage"
    });
  }
};
const istitle = computed(() => {
  return Boolean(route.query.title && route.query.title === "true");
});

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});
const getTableList = async () => {
  const { code, data, msg } = await organizationFindById();
  inalias.value = data?.organizationAdmin?.organizationAlias;
  getiphorgId.value = data?.id;
  alias.value = data?.alias;
};
const initRoleData = async () => {
  const params = { adminId: route.query?.id };
  const [err, result] = await requestTo(getNonCancell(params));
  if (!err) {
    disabledIds.value = result;
    await getroleList(result);
  }
};

const disabledIds = ref([]); // 保存被禁止操作的ID

const handleCheckboxClick = (id, event) => {
  if (disabledIds.value.includes(id)) {
    ElMessage.warning("该角色不可操作");
    return;
  }
  const index = form.value.roleIds.indexOf(id);
  if (index > -1) {
    form.value.roleIds.splice(index, 1);
  } else {
    form.value.roleIds.push(id);
  }
};

// 实时更新初始密码
const newPassword = ref("");

const generatePassword = (name, phone) => {
  try {
    const initials = pinyin(name || "", {
      pattern: "first",
      toneType: "none",
      type: "array"
    })
      .join("")
      .toLowerCase();

    const phonePart = phone?.slice(-6) || "";
    newPassword.value = `${initials}${phonePart}@`;
  } catch (error) {
    console.error("生成密码失败：", error);
    newPassword.value = "";
  }
};
watch(
  () => [form.value.name, form.value.phone],
  ([name, phone]) => {
    // 前置校验
    if (name && name.length >= 2 && phone && /^1[3-9]\d{9}$/.test(phone)) {
      generatePassword(name, phone);
    } else {
      newPassword.value = "";
    }
  }
);
const isViewFn = (val, index) => {
  formData.value[index].isView = !formData.value[index].isView;
  if (val === "idNumber") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value.idNumber;
    } else {
      // 切换为显示解密数据
      form.value[val] =
        oldData.value.idNumberDecrypted || decrypt(oldData.value.idNumberCt);
    }
  } else if (val === "phone") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value.phone;
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(oldData.value.phoneCt);
    }
  }
};

if (formData.value[3].label === "验证码") {
  formData.value[2].hasButton = false;
  formData.value.splice(3, 1);

  // 清除验证码字段的数据和可能的校验错误
  form.value.code = undefined;
  if (formRef.value) {
    formRef.value.clearValidate("code");
  }
}

// 微信登录初始化状态
const isWxLoginInitialized = ref(false);

// 微信回调处理状态
const isWxCallbackProcessed = ref(false);

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, "code");
    console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      handleWxCallback(code, state);
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="containers">
    <div v-if="istitle" class="table_top">
      <el-descriptions
        class="margin-top"
        title=""
        :column="2"
        border
        style="width: 100%"
        :label-width="'200px'"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div class="table_content">
      <el-form ref="formRef" :model="form" :rules="rules" style="flex-grow: 1">
        <el-descriptions
          v-if="richFlag"
          title=""
          :column="2"
          border
          :label-width="'200px'"
        >
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            :span="
              item.prop === 'phone'
                ? formData.some(i => i.prop === 'code')
                  ? 1
                  : 2
                : item.prop === 'code'
                  ? 1
                  : 2
            "
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.inline"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="
                    (item.prop === 'phone' || item.prop === 'idNumber') &&
                    item.isView &&
                    oldData[item.prop]?.length
                  "
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  @input="filterChineseInput($event, item.prop)"
                >
                  <template
                    v-if="
                      (item.prop === 'phone' || item.prop === 'idNumber') &&
                      form[item.prop]?.length > 0
                    "
                    #suffix
                  >
                    <div v-if="istitle">
                      <el-icon
                        v-if="item.isView"
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-else
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <View />
                      </el-icon>
                    </div>
                  </template>
                </el-input>
                <span v-if="item.prop === 'account'" class="aliastyle">@{{ alias }}</span>
                <!-- 获取验证码按钮移至验证码输入框后面 -->
                <div v-if="item.prop === 'code'" class="Vacode">
                  <el-button
                    v-countdown="{
                      value: 60,
                      callback: () => getCaptcha(form.phone),
                      countdownText: 's后重新获取',
                      loadingText: '发送中...'
                    }"
                  >
                    获取验证码
                  </el-button>
                </div>
              </template>
              <!-- 角色选择 -->
              <template v-if="item.type === 'checkbox'">
                <template v-if="item.type === 'checkbox'">
                  <el-checkbox-group v-model="form.roleIds">
                    <el-checkbox
                      v-for="(option, index) in item.list"
                      :key="index"
                      :label="option.id"
                      :class="{
                        'disabled-fake': disabledIds.includes(option.id)
                      }"
                      @click.stop.prevent="
                        handleCheckboxClick(option.id, $event)
                      "
                    >
                      {{ option.list_name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </template>
              </template>

              <!-- 二维码展示 -->
              <template v-else-if="item.type === 'img'">
                <span v-if="!form.isBindWx" class="isQR">
                  <!-- 当前{{ form.isBindWx ? "已绑定" : "未绑定" }} -->
                  当前未绑定
                </span>
                <div v-else>
                  <span>已绑定,
                    <el-link
                      type="primary"
                      underline="hover"
                      @click="showUnbindDialog = true"
                    >
                      解绑
                    </el-link>
                  </span>
                </div>
                <div v-if="!form.isBindWx" class="codeQR">
                  <!-- <el-image
                    :src="qrCodeData"
                    :style="{ height: item.height }"
                    :hide-on-click-modal="true"
                  /> -->
                  <!-- 只有在没有微信回调参数时才显示二维码 -->
                  <WxQrCode
                    v-if="!hasWxCallbackParams"
                    ref="wxQrCodeRef"
                    :redirectPath="redirectPathWithQuery"
                  />
                  <!-- 有微信回调参数时显示处理中状态 -->
                  <div v-else class="processing-status">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                    <span>正在处理微信绑定...</span>
                  </div>
                  <!-- <span>扫描二维码更换微信绑定</span> -->
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="!istitle" class="ChangePwd">
          <span>密码生成规则：姓名首字母+手机号后6位+@</span>
          <div v-if="newPassword">
            初始密码为：<span>{{ newPassword }}</span>
          </div>
        </div>
      </el-form>
      <div class="table_bottom">
        <el-button type="default" @click="cancelForm"> 取消 </el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ istitle ? "保存" : "确认新建" }}
        </el-button>
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑当前微信账号吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-dialog) {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0px !important;
}

.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 88vh;
  // padding: 24px;
  // background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }
    .aliastyle {
      margin-left: 20px;
    }
    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
    }

    .processing-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  }

  .ChangePwd {
    display: flex;
    justify-content: space-between;
    width: 30%;
    margin-top: 10px;
    font-size: smaller;
    font-weight: 600;
    white-space: nowrap;
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 5%;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
.disabled-fake {
  pointer-events: auto;
  color: #999 !important;
  cursor: not-allowed;

  .el-checkbox__inner {
    background-color: #f5f5f5 !important;
    border-color: #dcdfe6 !important;
  }
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>

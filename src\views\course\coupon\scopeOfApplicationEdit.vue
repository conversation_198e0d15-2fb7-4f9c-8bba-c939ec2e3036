<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch, PlusTable } from "plus-pro-components";
import { ElMessage } from "element-plus";
import CouponDetailInfo from "./components/couponDetailInfo.vue";

defineOptions({
  name: "ScopeOfApplicationEdit"
});

const router = useRouter();
const route = useRoute();

// 优惠券详情数据
const couponDetail = ref({
  couponName: "超级一家优惠券",
  status: "启用",
  usageScope: "指定",
  notes: "优惠券全家桶,一人一次。",
  couponType: "满减券",
  thresholdAmount: "满100减50",
  issuanceTime: "2025-10-11 至 2025-11-11",
  usageTime: "2025-11-11 至 2025-12-11",
  creationTime: "2024-10-12 16:00:11"
});

// 搜索表单数据
const searchForm = reactive({
  courseName: "",
  coursePeriodName: "",
  coursePeriodStatus: "不限"
});

// 搜索列配置
const searchColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    type: "input",
    placeholder: "请输入课程名称"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    type: "input",
    placeholder: "请输入课期名称"
  },
  {
    label: "课期状态",
    prop: "coursePeriodStatus",
    type: "select",
    options: [
      { label: "不限", value: "不限" },
      { label: "已上架", value: "已上架" },
      { label: "未上架", value: "未上架" }
    ]
  }
]);

// 表格列配置
const tableColumns = ref([
  {
    type: "selection",
    width: 55,
    align: "center"
  },
  {
    label: "期号",
    prop: "issueNumber",
    width: 100,
    align: "center"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName"
    // minWidth: 200
  },
  {
    label: "开课时间",
    prop: "startDate",
    width: 220,
    align: "center"
  },
  {
    label: "购买类型",
    prop: "purchaseType",
    width: 100,
    align: "center"
  },
  {
    label: "课期状态",
    prop: "coursePeriodStatus",
    width: 100,
    align: "center",
    formatter: ({ coursePeriodStatus }) => {
      if (coursePeriodStatus === "已上架") {
        return '<span style="color: #67c23a;">已上架</span>';
      } else if (coursePeriodStatus === "未上架") {
        return '<span style="color: #909399;">未上架</span>';
      }
      return coursePeriodStatus;
    }
  }
]);

// 假数据 - 课程列表
const tableData = ref([
  {
    id: 1,
    issueNumber: 1,
    coursePeriodName: "新用户专享",
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 2,
    issueNumber: 2,
    coursePeriodName: "老客户回归",
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 3,
    issueNumber: 3,
    coursePeriodName: "轻松一夏",
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 4,
    issueNumber: 4,
    coursePeriodName: "快乐一起营",
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "未上架"
  },
  {
    id: 5,
    issueNumber: 5,
    coursePeriodName: "机构5",
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "未上架"
  }
]);

// 分页配置
const pagination = reactive({
  page: 1,
  size: 5,
  total: 25
});

// 加载状态
const loading = ref(false);

// 选中的行
const selectedRows = ref([]);

// 搜索方法
const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    console.log("搜索条件:", searchForm);
    loading.value = false;
  }, 500);
};

// 重置方法
const onReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === "coursePeriodStatus" ? "不限" : "";
  });
  onSearch();
};

// 分页改变
const onPageChange = page => {
  pagination.page = page;
  onSearch();
};

// 每页条数改变
const onSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  onSearch();
};

// 选择改变
const onSelectionChange = selection => {
  selectedRows.value = selection;
  console.log("选中的行:", selection);
};

// 确认修改
const onConfirm = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请至少选择一个课程");
    return;
  }

  ElMessage.success(`已选择 ${selectedRows.value.length} 个课程`);
  console.log("确认修改，选中的课程:", selectedRows.value);
};

// 取消操作
const onCancel = () => {
  ElMessage.info("已取消修改");
  router.go(-1);
};

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 150px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 340px - ${searchFormHeight.value}px)`;
  }
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- 优惠券详情 -->
      <div class="coupon-detail">
        <CouponDetailInfo :showUseScope="false" />
      </div>

      <div class="con-content">
        <!-- 搜索区域 -->
        <div class="con_search">
          <PlusSearch
            v-model="searchForm"
            :columns="searchColumns"
            :show-number="3"
            :label-width="80"
            :hasUnfold="false"
            :searchOnChange="false"
            @search="onSearch"
            @reset="onReset"
          />
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <PlusTable
            v-loading="loading"
            :data="tableData"
            :columns="tableColumns"
            :title-bar="false"
            :border="false"
            :is-selection="true"
            :max-height="tableHeight"
            @selection-change="onSelectionChange"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.page"
            :page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[5, 10, 15, 20]"
            width="100%"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onConfirm"> 确认 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.breadcrumb {
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.coupon-detail {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
}

.label {
  color: #606266;
  font-weight: 500;
  min-width: 120px;
  margin-right: 8px;
}

.value {
  color: #303133;
  flex: 1;
}

.status-enabled {
  color: #67c23a;
  font-weight: 500;
}

.con_search {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  /* margin-bottom: 16px; */

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

.table-container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  height: 320px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin: 60px 0 16px 0;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 0 20px 15px 0;
  background-color: #fff;
}

/* 表格样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-col) {
  max-width: 330px !important;
}

/* 搜索按钮样式 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

/* 选择框样式 */
:deep(.el-table__selection) {
  .el-checkbox__inner {
    border-color: #dcdfe6;
  }
}

:deep(.el-table__selection .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .containers {
    padding: 10px;
  }

  .detail-row {
    flex-direction: column;
    gap: 12px;
  }

  .detail-item {
    min-height: auto;
  }
}
.con-content {
  height: 590px;
  background-color: #ffffff;
}
</style>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import CouponDetailInfo from "./components/couponDetailInfo.vue";
defineOptions({
  name: "CouponManagementDetail"
});

const router = useRouter();
const route = useRoute();
// 优惠券详情数据
const couponDetail = ref({
  couponName: "超级一家优惠券",
  couponType: "满减券",
  status: "启用",
  discountRule: "满100减50",
  createTime: "2024-10-12 16:00:11",
  issueTime: "2025-10-11 至 2025-11-11",
  useTime: "2025-11-11 至 2025-12-11",
  notes: "优惠券全场，一人一次。"
});

const detailConfig = reactive([
  {
    label1: "优惠券名称",
    prop1: "couponName",
    label2: "优惠券类型",
    prop2: "couponType"
  },
  {
    label1: "状态",
    prop1: "status",
    label2: "优惠门槛与金额",
    prop2: "discountRule"
  },
  {
    label1: "创建时间",
    prop1: "createTime",
    label2: "发放时间",
    prop2: "issueTime"
  },
  {
    label1: "备注",
    prop1: "notes",
    label2: "使用时间",
    prop2: "useTime"
  }
]);

// 搜索表单数据
const searchForm = reactive({
  userAccount: "",
  getMethod: "",
  couponStatus: ""
});

// 表格列配置
const columns = ref([
  {
    label: "用户账号",
    prop: "userAccount",
    minWidth: 120
  },
  {
    label: "昵称",
    prop: "nickname",
    minWidth: 100
  },
  {
    label: "领取时间",
    prop: "receiveTime",
    minWidth: 150
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150
  },
  {
    label: "优惠券使用类型",
    prop: "couponUseType",
    minWidth: 120
  },
  {
    label: "优惠说明",
    prop: "description",
    minWidth: 150
  },
  {
    label: "获取方式",
    prop: "getMethod",
    minWidth: 100
  },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 统计数据
const statistics = ref({
  issued: 350,
  received: 350,
  used: 350
});

// Mock数据
const mockTableData = [
  {
    id: 1,
    userAccount: "135****2457",
    nickname: "老张",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "材料",
    description: "开元山庄松的课程",
    getMethod: "在线领取"
  },
  {
    id: 2,
    userAccount: "135****2457",
    nickname: "老王",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "通用",
    description: "开元山庄松的课程",
    getMethod: "在线领取"
  },
  {
    id: 3,
    userAccount: "135****2457",
    nickname: "老方",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "材料",
    description: "开元山庄松的课程",
    getMethod: "手动派发"
  },
  {
    id: 4,
    userAccount: "135****2457",
    nickname: "老老",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "",
    couponUseType: "",
    description: "",
    getMethod: ""
  },
  {
    id: 5,
    userAccount: "135****2457",
    nickname: "网友",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "",
    couponUseType: "",
    description: "",
    getMethod: ""
  }
];

// 加载表格数据
const loadTableData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockTableData;
    pagination.total = mockTableData.length;
    loading.value = false;
  }, 500);
};

// 搜索处理
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  loadTableData();
};

// 新增优惠券
const handleAddCoupon = () => {
  router.push("/coupon/management/create");
};

// 关联订单操作
const handleRelateOrder = row => {
  ElMessage.info(`关联订单: ${row.userAccount}`);
};

// 返回
const handleBack = () => {
  router.go(-1);
};

onMounted(() => {
  // 获取路由参数中的优惠券ID
  const couponId = route.params.id;
  console.log("优惠券ID:", couponId);

  // 加载优惠券详情和表格数据
  loadTableData();
});
</script>

<template>
  <div class="coupon-detail">
    <!-- 优惠券详情展示区域 -->
    <div class="common detail-container">
      <CouponDetailInfo :id="route.query.id" />
    </div>

    <!-- 表格区域 -->
    <div class="common table-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="coupon-search-container">
          <div class="search-items">
            <div class="search-item">
              <span class="search-label">用户账号</span>
              <el-input
                v-model="searchForm.userAccount"
                placeholder="请输入用户账号"
                style="width: 200px"
                clearable
              />
            </div>
            <div class="search-item">
              <span class="search-label">获取方式</span>
              <el-select
                v-model="searchForm.getMethod"
                placeholder="请选择获取方式"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="在线领取" value="online" />
                <el-option label="手动派发" value="manual" />
              </el-select>
            </div>
            <div class="search-item">
              <span class="search-label">优惠券状态</span>
              <el-select
                v-model="searchForm.couponStatus"
                placeholder="请选择状态"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="未使用" value="unused" />
                <el-option label="已使用" value="used" />
                <el-option label="已过期" value="expired" />
              </el-select>
            </div>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 标题和统计信息 -->
      <div class="title-statistics">
        <div class="table-title">
          <h3>领取记录</h3>
        </div>
        <div class="statistics">
          <span class="stat-item">发放数量：{{ statistics.issued }}</span>
          <span class="stat-item">领取数量：{{ statistics.received }}</span>
          <span class="stat-item">使用数量：{{ statistics.used }}</span>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-section">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 180 }"
          align-whole="left"
          table-layout="auto"
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="loadTableData"
          @page-current-change="loadTableData"
        >
          <template #operation="{ row }">
            <div class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleRelateOrder(row)"
              >
                关联订单
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>

      <!-- 返回按钮 -->
      <div class="footer-actions">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;

    &.table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .detail-card {
    padding: 4px 0;
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .search-section {
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .coupon-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-items {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .search-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
    }
  }

  .title-statistics {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .table-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .statistics {
    display: flex;
    gap: 40px;

    .stat-item {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
// :deep(.my-label) {
//   background: #fff !important;
// }
// :deep(
//   .el-descriptions__body
//     .el-descriptions__table.is-bordered
//     .el-descriptions__cell
// ) {
//   border: 0px !important;
// }
// :deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
//   font-weight: normal;
// }
</style>

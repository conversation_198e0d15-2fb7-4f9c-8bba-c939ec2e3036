import { aiCourse } from "@/router/enums";
import { aiCodesList } from "@/router/accidCode.js";
import { aiNewPage } from "@/utils/aiTool.js";
import AIIcon from "@/assets/home/<USER>";
import AIIconActive from "@/assets/home/<USER>";

const Layout = () => import("@/layout/index.vue");
// 创建一个空白组件，用于路由配置
const EmptyComponent = {
  render() {
    return null;
  },
  beforeRouteEnter(to, from, next) {
    // 在导航进入该组件对应的路由时调用aiNewPage函数
    aiNewPage();
    // 完成导航，然后立即重定向回来源页面
    next();
  },
  mounted() {
    // 组件挂载后，立即重定向回来源页面
    this.$router.go(-1);
  }
};

export default {
  path: "/ai-course",
  name: "AICourse",
  component: Layout,
  redirect: "/ai-course",
  meta: {
    icon: "ep:magic-stick", // 使用Element Plus的魔法棒图标
    imgIcon: AIIcon,
    imgIconActive: AIIcon,
    // imgIconActive: AIIconActive,
    title: "AI课程设计",
    rank: aiCourse,
    idCode: aiCodesList.baseCode,
    showLink: true
  },
  children: [
    {
      path: "/ai-course",
      name: "AICourseDesign",
      component: EmptyComponent,
      meta: {
        title: "AI课程设计",
        idCode: aiCodesList.pending,
        showLink: true
      }
    }
  ]
};

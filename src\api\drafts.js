import { http } from "@/utils/http";

/*  草稿箱相关接口  */
// 分页查询
export const draftFindAll = params => {
  return http.request(
    "get",
    "/organization/draft/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询草稿数量
export const draftGetCount = params => {
  return http.request(
    "get",
    "/organization/draft/getCount",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿Id查询草稿完成度
export const draftFindFlowsByDraftId = params => {
  return http.request(
    "get",
    "/organization/draft/findFlowsByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 删除
export const draftDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draft/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};

/*  草稿行程相关接口  */
// 根据草稿Id查询草稿行程
export const ItineraryFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftItinerary/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 草稿行程下一步
export const ItineraryNextDraftItinerary = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftItinerary/nextDraftItinerary",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 保存草稿行程
export const itinerarySaveDraftItinerary = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftItinerary/saveDraftItinerary",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 草稿箱基本信息
// 根据草稿Id查询课程草稿
export const findByCourseDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCourse/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿Id查询课期草稿
export const findByCoursePeriodDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCoursePeriod/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 基本信息下一步
export const nextBasicInformation = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draft/nextBasicInformation",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 保存基本信息
export const saveBasicInformation = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draft/saveBasicInformation",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};

// 课期草稿相关接口
// 根据草稿Id查询课程草稿
export const draftCoursePeriodFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCoursePeriod/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//根据草稿Id查询课程草稿
export const draftCoursePeriodCountByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCoursePeriod/countByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 课程介绍草稿相关接口
// 根据草稿Id查询课程介绍草稿
export const draftCourseIntroductionFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCourseIntroduction/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存课程介绍草稿
export const saveDraftCourseIntroduction = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftCourseIntroduction/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 课程介绍草稿下一步
export const saveCourseIntroductionNext = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftCourseIntroduction/next",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 草稿箱知识点相关接口
// 根据草稿Id查询
export const draftCourseKnowledgePointFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftCourseKnowledgePoint/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 更新草稿知识点
export const draftCourseKnowledgePoint = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftCourseKnowledgePoint/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 保存——下一步
export const saveDraftCourseKnowledgePointNext = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftCourseKnowledgePoint/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 删除单条数据
export const deleteDraftCourseKnowledgePoint = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftCourseKnowledgePoint/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 根据id查询
export const findByIdDraftCourseKnowledgePoint = params => {
  return http.request(
    "get",
    "/organization/draftCourseKnowledgePoint/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 草稿箱作业设计（实践感悟）相关接口
// 根据草稿Id查询
export const draftAssignmentDesignFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftAssignmentDesign/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存-下一步草稿作业设计（实践感悟）
export const saveDraftAssignmentDesign = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftAssignmentDesign/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 草稿箱材料说明相关接口
// 根据草稿Id查询
export const draftEquipmentDescriptionFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftEquipmentDescription/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存-下一步材料说明草稿
export const saveDraftEquipmentDescription = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftEquipmentDescription/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 草稿箱注意事项相关接口
// 根据草稿Id查询
export const draftPrecautionsFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftPrecautions/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存-下一步注意事项草稿
export const saveDraftPrecautions = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftPrecautions/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 草稿箱用户协议相关接口
// 根据草稿Id查询
export const draftUserAgreementFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftUserAgreement/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存-用户协议草稿
export const saveDraftUserAgreement = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftUserAgreement/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 下一步用户协议草稿
export const saveDraftUserAgreementNext = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftUserAgreement/next",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
// 价格设置草稿相关接口
// 根据草稿Id查询价格设置(费用说明，退款政策)
export const priceSettingFindByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftPriceSetting/findByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿Id查询规格说明、规格选项
export const findSpecificationNameOptionByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftPriceSetting/findSpecificationNameOptionByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿查询规格表格
export const findSpecificationTableByDraftId = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findSpecificationTableByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿查询价格数量
export const findSpecificationByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftPriceSetting/findSpecificationByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据草稿Id查询价格明细
export const findFeeItemByDraftId = params => {
  return http.request(
    "get",
    "/organization/draftPriceSetting/findFeeItemByDraftId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 保存价格设置草稿
export const saveDraftPriceSetting = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draftPriceSetting/saveV2",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};

// 草稿创建完成相关接口
// 创建-完成
export const coursePeriodSaveV2 = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/saveV2",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};

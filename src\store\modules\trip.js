import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { getObjVal } from "@iceywu/utils";
export const useTripStore = defineStore(
  "trip",
  () => {
    // 行程信息
    const draftTrip = ref([]);
    // 实践点
    const basePlace = ref();
    // 讲师
    const lecturerData = ref();
    // 存储行程信息
    const saveDraftTrip = data => {
      draftTrip.value = data;
    };
    const saveBasePlace = data => {
      basePlace.value = data;
    };

    // 存储讲师
    const saveLecturerData = data => {
      lecturerData.value = data;
    };
    return {
      draftTrip,
      saveDraftTrip,
      basePlace,
      saveBasePlace,
      lecturerData,
      saveLecturerData
    };
  },
  {
    persist: {
      key: "tripInfo"
    }
  }
);

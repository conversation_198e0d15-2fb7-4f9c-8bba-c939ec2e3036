# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.14.4](https://github.com/pure-admin/vue-pure-admin/compare/v1.14.3...v1.14.4) (2025-08-19)

### Bug Fixes

- 材料说明等默认值还原 ([16b03f1](https://github.com/pure-admin/vue-pure-admin/commit/16b03f136a422427b8a6d05244f2765c7fb52024))
- 下架理由选择末尾不要逗号 ([c4256bb](https://github.com/pure-admin/vue-pure-admin/commit/c4256bb9a20298ad1f2537e58e8e3ba3e10dcc36))

### [1.14.3](https://github.com/pure-admin/vue-pure-admin/compare/v1.14.2...v1.14.3) (2025-08-19)

### Bug Fixes

- 调整报名管理表格高度 ([86049cd](https://github.com/pure-admin/vue-pure-admin/commit/86049cd0cdda936e70416d8c75098a56f1052d18))
- 调整报名管理表格样式修改 ([2a67d5f](https://github.com/pure-admin/vue-pure-admin/commit/2a67d5fd9c3ea193a1295239b43eb5afdc3ba37e))
- 课程介绍只传图片等显示为空 ([851db9f](https://github.com/pure-admin/vue-pure-admin/commit/851db9fb3daa345083321db231b45c02f26059c8))

### [1.14.2](https://github.com/pure-admin/vue-pure-admin/compare/v1.14.1...v1.14.2) (2025-08-18)

### Bug Fixes

- 调整AI课程和首页路由的重定向逻辑及图标配置 ([b069f47](https://github.com/pure-admin/vue-pure-admin/commit/b069f475af9c9e2f956fde6539bc719a08d6eda6))
- 二维码环境配置调整 ([145df6d](https://github.com/pure-admin/vue-pure-admin/commit/145df6d1d6d179f46d39f1ea0b0c9440e19f32fb))
- 将实践点名称可编辑 ([11b83ec](https://github.com/pure-admin/vue-pure-admin/commit/11b83ec332408383a1f016a6df79d8e1228e391e))
- 中山环境app_id调整 ([31881b2](https://github.com/pure-admin/vue-pure-admin/commit/31881b2d5bb6a88879df20c38ec43427b52cec27))

### [1.14.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.14.0...v1.14.1) (2025-08-18)

### Bug Fixes

- 草稿箱完成页非全屏屏幕完成新建按钮消失 ([bd4a990](https://github.com/pure-admin/vue-pure-admin/commit/bd4a99044c8dfb426b14c54a9732b5836839ee2b))
- 草稿箱移除HTML标签后检查空内容时设置为null ([525bc74](https://github.com/pure-admin/vue-pure-admin/commit/525bc7492ad47517ffeb3dcd17b841144c52db51))
- 课程详情上下架改为上下架申请及课期详情视频优化 ([f484434](https://github.com/pure-admin/vue-pure-admin/commit/f484434a00866646f5d55692bca9769514c12817))
- 课期复制报名截止日期不正确 ([a822046](https://github.com/pure-admin/vue-pure-admin/commit/a822046bfd8c37e0a8233eac2c383562baa12be3))
- 课期复制讲师id不为空 ([3915283](https://github.com/pure-admin/vue-pure-admin/commit/3915283c92c2fa2a2cefbc2cb7db5a0d87885353))
- 行程安排实践点和讲师显示 ([d396704](https://github.com/pure-admin/vue-pure-admin/commit/d396704b47fb8154287be5ae8e7e00a19d903afb))

## [1.14.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.13.0...v1.14.0) (2025-08-15)

### Features

- 报名管理导出功能，实现数据导出异步任务处理 ([e88c23c](https://github.com/pure-admin/vue-pure-admin/commit/e88c23caf829baabcfc53ebda8934e59846c8343))
- 订单详情新增子订单原价展示 ([fe35dd0](https://github.com/pure-admin/vue-pure-admin/commit/fe35dd0a1e6037946070a0d5d0879b1105d7727b))
- 课程改期动态 ([5a5727b](https://github.com/pure-admin/vue-pure-admin/commit/5a5727bade014126db212b35f5c4f6ff607139c0))
- 课程详情不同改期状态申请改期阿牛展示不同tooltip ([c4a930c](https://github.com/pure-admin/vue-pure-admin/commit/c4a930c7c0447ee39090ceac8fba379d746f5794))
- 添加促销规则禁用时的提示图标 ([1483c5e](https://github.com/pure-admin/vue-pure-admin/commit/1483c5edb0f78e2caa7789596f43788b534ff1c1))
- 添加主订单原价计算并显示 ([4a9136f](https://github.com/pure-admin/vue-pure-admin/commit/4a9136f586f3e4bdcf2e3e8f3a2ef3bed81570ba))
- 新增报名管理功能，包括列表页、课期详情页及相关API接口 ([ec03cc4](https://github.com/pure-admin/vue-pure-admin/commit/ec03cc4b324e17e41112018bb45eba7d3fee5fcc))
- 新增课程草稿促销价功能，支持设置促销价及相关规则，优化价格输入处理 ([29e5613](https://github.com/pure-admin/vue-pure-admin/commit/29e5613f0e4c404f99ef507570c5c67627fc4185))
- 新增课期详情价格设置的促销价相关处理 ([9c89ba2](https://github.com/pure-admin/vue-pure-admin/commit/9c89ba29825c3b202816568a656db5c63356d127))
- 新增申请改期弹窗组件，并在课程详情页集成改期功能 ([c3dbf4a](https://github.com/pure-admin/vue-pure-admin/commit/c3dbf4a18831276f9ca6da78a8d288d596f21818))
- 在财务表格中添加原价列显示 ([9031f87](https://github.com/pure-admin/vue-pure-admin/commit/9031f8740098ef7004b94da2188014dd52298596))
- 重构地区选择功能，使用静态JSON数据替代API调用 ([3619518](https://github.com/pure-admin/vue-pure-admin/commit/361951838c0490047b383241a889d5584378b347))

### Bug Fixes

- 草稿箱、编辑、详情行程安排调整 ([d2a7b17](https://github.com/pure-admin/vue-pure-admin/commit/d2a7b17b972ade3d1fc1f6fe152f1698aded5605))
- 草稿箱部分接口调整 ([b2ef240](https://github.com/pure-admin/vue-pure-admin/commit/b2ef24007375d3a7ec00b91ac13eb33fb624d379))
- 调整报名管理表格样式及列宽设置 ([542a374](https://github.com/pure-admin/vue-pure-admin/commit/542a3749c496c9495a95edd047d957c11f891c19))
- 调整课期知识点保存逻辑及步骤导航依赖关系 ([a0b710a](https://github.com/pure-admin/vue-pure-admin/commit/a0b710aac256555d001aa093526a06ac63e217af))
- 复制课期调整 ([c94f19e](https://github.com/pure-admin/vue-pure-admin/commit/c94f19ef300f24f87dbdbe5061fa2ec58925fab2))
- 将'课程名'和'课程期号'统一修改为'课期名称'和'课期号' ([e8e5a1c](https://github.com/pure-admin/vue-pure-admin/commit/e8e5a1c7167bb5777b4b24d5d699fe29492e9587))
- 课期报告和实践感悟页面优化 ([de8614a](https://github.com/pure-admin/vue-pure-admin/commit/de8614a44f396fa781d456fb5b8716582bf8e894))
- 课期编辑领队调整 ([76add85](https://github.com/pure-admin/vue-pure-admin/commit/76add85fe27d65f4d1aadb88fe48da54b6ed18d0))
- 课期行程安排相关调整 ([5b759e5](https://github.com/pure-admin/vue-pure-admin/commit/5b759e5999e4b1aea3cb62e44b0964c73cdbb5ee))
- 课堂追踪视频展示 ([ea3b5a2](https://github.com/pure-admin/vue-pure-admin/commit/ea3b5a21bb13770b5ae249d3f5d768f0810e44ee))
- 批量删除操作说明 ([13c245a](https://github.com/pure-admin/vue-pure-admin/commit/13c245a981faf25aca07da795ebefb2a9b462e12))
- 屏幕缩小完成新建按钮消失问题 ([119949e](https://github.com/pure-admin/vue-pure-admin/commit/119949e0d44e92584da74a64d32a3def8605d26d))
- 切换开发环境API服务器地址为k8s-dev ([292fb38](https://github.com/pure-admin/vue-pure-admin/commit/292fb385a745aa25fb60c3c74bf35ad15d04680c))
- 视频第一次加载未显示问题 ([22b4722](https://github.com/pure-admin/vue-pure-admin/commit/22b472279308c2eaa9b20442704789efa595ce3c))
- 修复促销规则校验并更新价格设置API路径 ([1cc6b30](https://github.com/pure-admin/vue-pure-admin/commit/1cc6b3040d393e1ee5e0b17d2585fe67f892b74a))
- 修复改期时间戳上传为毫秒级 ([4b8d773](https://github.com/pure-admin/vue-pure-admin/commit/4b8d773f865fbdb5fad5e6a38eec0f0b40da4f59))
- 修复金额输入验证并添加最大值限制 ([a68ac23](https://github.com/pure-admin/vue-pure-admin/commit/a68ac2371550165fa2d6bf55a6d46622893b7906))
- 修改机构新增/编辑页面地址字段提示文本为"请选择归属地 ([cf3819c](https://github.com/pure-admin/vue-pure-admin/commit/cf3819ca597f3b7d54c3aefcdaa31df17e1947ba))
- 优化报名管理表格样式，移除边框并设置表头背景色为白色 ([4e92832](https://github.com/pure-admin/vue-pure-admin/commit/4e928327c05de5729d1fad4823500b16f154e686))
- 优化财务管理和订单管理页面的按钮样式及交互，新增课程名点击事件 ([24eb7bd](https://github.com/pure-admin/vue-pure-admin/commit/24eb7bdb4cfcf86c039d73aabc1b3b68c56ba758))
- fix: 课期详情视频第一次预览加载未显示 ([69bff33](https://github.com/pure-admin/vue-pure-admin/commit/69bff337346d9f0014d51ed3d766165802a5e380))

## [1.13.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.12.0...v1.13.0) (2025-08-09)

### Features

- 编辑新增课程视频及视频封面上传 ([0e61df7](https://github.com/pure-admin/vue-pure-admin/commit/0e61df72edd1e1bf8d242d9dadf516100b362d3a))
- 财务管理订单管理新增跳转课期 ([f53e2eb](https://github.com/pure-admin/vue-pure-admin/commit/f53e2eb886db20ab85ca325348047e7d48579632))
- 草稿箱完成阶段价格设置回显 ([4b209bf](https://github.com/pure-admin/vue-pure-admin/commit/4b209bf923915f589eb1534f8f5c1cc8f75899c1))
- 更新财务管理展示字段 ([eaf0e01](https://github.com/pure-admin/vue-pure-admin/commit/eaf0e01afedaedf0eedebac24b4a242dfa22714b))
- 更新订单管理和价格设置接口，新增根据课期Id查价格明细功能，调整退款状态和类型显示 ([8aef6ae](https://github.com/pure-admin/vue-pure-admin/commit/8aef6aef183463c40b9d0ade4b2535dbefc3ee8f))
- 更新价格设置接口，调整费用项目解析逻辑，新增子订单退款金额计算功能 ([c6c2dd6](https://github.com/pure-admin/vue-pure-admin/commit/c6c2dd61b4d4a0111a82211ace8b6d8d89a2ea42))
- 机构管理新增地区级联选择功能 ([0100f19](https://github.com/pure-admin/vue-pure-admin/commit/0100f19898961d6489a423fc5f5efd21b23c4c25))
- 价格设置规格替换为课程费用和材料费用输入 ([5f147f3](https://github.com/pure-admin/vue-pure-admin/commit/5f147f39588dfedb96e2281011a41dff03b4064c))
- 课程详情页面新增课程简介展示及样式调整 ([b3953d6](https://github.com/pure-admin/vue-pure-admin/commit/b3953d6d8ccf2c7a25c01ff0aa5c54314da6c0cc))
- 课程详情页面新增收缩功能 ([1aeccef](https://github.com/pure-admin/vue-pure-admin/commit/1aeccef771c791a3e4b7cc551aa0b76af1b64d9d))
- 课期下架申请申请理由弹窗 ([19d063b](https://github.com/pure-admin/vue-pure-admin/commit/19d063b1a6c5cb35afce5f82c3acb67d38b5656a))
- 新增费用明细和购买金额计算功能，调整订单详情展示 ([d836f19](https://github.com/pure-admin/vue-pure-admin/commit/d836f19f4c936a932574636999a66f51914c0115))
- 新增课程及编辑课程添加人数下限及关闭报名时间至少提前12小时 ([3e034d4](https://github.com/pure-admin/vue-pure-admin/commit/3e034d4bfea755342564e2c484602018e2bda167))
- 新增批量处理退单功能，包括批量驳回和确认退单的弹窗及相关逻辑 ([94c2571](https://github.com/pure-admin/vue-pure-admin/commit/94c25712194669449b02841644f52bc2b8ae7ea1))
- 新增退款驳回弹窗及相关逻辑，调整费用明细显示 ([d04c3e8](https://github.com/pure-admin/vue-pure-admin/commit/d04c3e88ebb3d2c99e42f65de21c2246503ca1f0))
- 新增子订单和主订单退单功能 ([a67fa4e](https://github.com/pure-admin/vue-pure-admin/commit/a67fa4ecd121592b3d950bba6b90e9643e5386b1))
- 学生情况展示购买材料人数及是否购买材料费 ([86798a8](https://github.com/pure-admin/vue-pure-admin/commit/86798a8edc0e99fa3efe9fbcdf1135f31c873764))
- 优化课程和材料费用输入处理，增加输入验证及回显逻辑 ([9fcb154](https://github.com/pure-admin/vue-pure-admin/commit/9fcb1544b7e7ccdb84e919728d3c77679a5e38f6))
- 在财务管理页面新增订单号和课期名称字段展示 ([1373cab](https://github.com/pure-admin/vue-pure-admin/commit/1373cab987cc050686909deab2b0c5d4d139ad4c))

### Bug Fixes

- 编辑新增课期人数限制及编辑限制截止报名时间 ([e7e826a](https://github.com/pure-admin/vue-pure-admin/commit/e7e826ab5febb09fb4dbd60129e2a0ca62d0184d))
- 草稿箱完成页退出跳转调整 ([ea7ab54](https://github.com/pure-admin/vue-pure-admin/commit/ea7ab54d95531c5a5e13f3e399426e6c04032272))
- 调整财务管理和订单管理页面表格列宽及样式 ([ca6226f](https://github.com/pure-admin/vue-pure-admin/commit/ca6226f256089a574699bb7bf746ab3155eedb77))
- 调整课程详情页面右上角伸缩按钮的位置 ([4e5329a](https://github.com/pure-admin/vue-pure-admin/commit/4e5329aafc8dfa3f3463bac97319a0775749f352))
- 调整课程详情页面中课程封面图显示分辨率 ([480a9fc](https://github.com/pure-admin/vue-pure-admin/commit/480a9fce8feb0d70ea5d2a225e1abcccd8a7ffda))
- 调整实践点表单中一句话简介和实践点介绍的字段顺序和布局 ([6166782](https://github.com/pure-admin/vue-pure-admin/commit/6166782ee3e56187d6984bb3073aec2c6ed162f2))
- 调整实践点表单字段顺序和必填校验，新增类型和地址展示列 ([cb721d4](https://github.com/pure-admin/vue-pure-admin/commit/cb721d4e4463146851cc4fb539eaf76049ae8f3c))
- 调整知识点表格列宽，修复机构管理页面数组长度判断 ([a6e5e64](https://github.com/pure-admin/vue-pure-admin/commit/a6e5e64eb75ee1ebd940ac878019145d95213c57))
- 调整知识点页面表格列宽及布局样式 ([84cb207](https://github.com/pure-admin/vue-pure-admin/commit/84cb2073e47263cd9332a514192321269d11e5da))
- 价格设置费用说明、报告、作业页面调整 ([b185ea3](https://github.com/pure-admin/vue-pure-admin/commit/b185ea3c565ca632eacf33bac7193f09d815bd94))
- 课程详情撤销申请渲染 ([e7cec3c](https://github.com/pure-admin/vue-pure-admin/commit/e7cec3c6853276edbcbac9ba966569dc49568baf))
- 课期编辑和课程编辑说明样式调整 ([d9be3ab](https://github.com/pure-admin/vue-pure-admin/commit/d9be3ab5cddc497e2eaa705b7cb147fe095fb528))
- 课期详情审核驳回原因展示优化 ([b76a836](https://github.com/pure-admin/vue-pure-admin/commit/b76a8369fe1ff130d04a172defef37a2c3facfc8))
- 课期新增封面修改后调整 ([bb82ee8](https://github.com/pure-admin/vue-pure-admin/commit/bb82ee842ec79c589c41ff8088642ea449c21264))
- 课期新增通过面包屑跳转储存信息清空 ([db505f0](https://github.com/pure-admin/vue-pure-admin/commit/db505f002a2b6617f5279da1c2c9ecbcbadceb4b))
- 课期新增完成页显示视频 ([9019f49](https://github.com/pure-admin/vue-pure-admin/commit/9019f495b4d65b0d7e025eb32a9aaede6a99cea7))
- 完成展示课期知识点 ([4b291a7](https://github.com/pure-admin/vue-pure-admin/commit/4b291a78cd822cb2eabe7ae5bd957e66594e7012))
- 行程安排时间调整 ([6941fc1](https://github.com/pure-admin/vue-pure-admin/commit/6941fc1956e4ababa3a67763b39e2e0fd4b83b2e))
- 修复财务管理订单管理跳转面包屑 ([333e40c](https://github.com/pure-admin/vue-pure-admin/commit/333e40ce5f2929d5f8fb2e05401b0bb47c9f5610))
- 学生情况加上人数下限材料费 ([2249440](https://github.com/pure-admin/vue-pure-admin/commit/224944082d3df812f478c8b6d7c036e498104310))
- 优化知识点保存逻辑，下一步没有填写数据无法保存 ([a10689f](https://github.com/pure-admin/vue-pure-admin/commit/a10689fe2b0e6674f64a32e9914184719430d687))

## [1.12.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.11.0...v1.12.0) (2025-08-02)

### Features

- 调整课程市场页面样式 ([77b58cf](https://github.com/pure-admin/vue-pure-admin/commit/77b58cfc6b6f18e528dba7371e83d469e8092adf))
- 订单退款新增退款理由展示 ([c4d2a71](https://github.com/pure-admin/vue-pure-admin/commit/c4d2a717bd129b87da6243b54950bf9ceeef37bc))
- 复制和编辑课期添加前后缀 ([e0128a6](https://github.com/pure-admin/vue-pure-admin/commit/e0128a61b48e86bb8b9167f011bc34200d30e112))
- 更新专家评价组件导入路径 ([7352a71](https://github.com/pure-admin/vue-pure-admin/commit/7352a71728a318acf8b626a2584a8a944a413657))
- 结束邀请常驻接口 ([8927499](https://github.com/pure-admin/vue-pure-admin/commit/892749968f89d685ab99ced73877a4120e51e7c1))
- 课程价格设置条目名称不能为价格和数量 ([0ee5839](https://github.com/pure-admin/vue-pure-admin/commit/0ee58395e8296c7f58acdbb2d04ff7aeddd6cc17))
- 课程市场 ([75459ee](https://github.com/pure-admin/vue-pure-admin/commit/75459eee7a35a4a22be97b2ccb2145498d120139))
- 课程市场页面弹窗，招募需求详情，课程市场分页 ([524a562](https://github.com/pure-admin/vue-pure-admin/commit/524a562a4e8ed10cd6c5b6cc6a9b500f6137ed56))
- 课程市场页面新增发布课程功能及路由配置 ([3136aad](https://github.com/pure-admin/vue-pure-admin/commit/3136aad04d9d0147f1a35814c90b57838764981d))
- 课程详情新增课程年龄段展示 ([c4e9a0d](https://github.com/pure-admin/vue-pure-admin/commit/c4e9a0dfb77439c801615d2fec3fc56e47d0b5e5))
- 日志新增按操作描述搜索 ([eca055b](https://github.com/pure-admin/vue-pure-admin/commit/eca055bd79b4c979ff2eba6424fb8e62042db43a))
- 师资侧边栏显示 ([85799c9](https://github.com/pure-admin/vue-pure-admin/commit/85799c91aeffc4feafb669c1f6dca57ab9c2b15a))
- 师资发布详情页面新增专家信息与评价组件 ([131cdb3](https://github.com/pure-admin/vue-pure-admin/commit/131cdb3afec94ee03d7031547b2af39cf74781c2))
- 师资库页面及接口、邀请管理和机构师资页面 ([f58a629](https://github.com/pure-admin/vue-pure-admin/commit/f58a6291a4ebda41c1ad9334ca71c023d0dc13a3))
- 师资详情 ([7d0bef6](https://github.com/pure-admin/vue-pure-admin/commit/7d0bef69c50fa1bbc025a6fa9afae3f1687888b3))
- 添加师资 ([543fc95](https://github.com/pure-admin/vue-pure-admin/commit/543fc95014d81611beed3e059f4c96634ca70466))
- 添加师资接口 ([05e5aae](https://github.com/pure-admin/vue-pure-admin/commit/05e5aaefb810d18bb93d4959765a1f34f45caf6f))
- 新增机构发布详情详情路由 ([45848ce](https://github.com/pure-admin/vue-pure-admin/commit/45848ce8a7d05359a802d8086d18fa03c0c705e1))
- 新增课程添加师资页面 ([186c8df](https://github.com/pure-admin/vue-pure-admin/commit/186c8df56b728bc830914a1a230e75cf1bd154fd))
- 新增形成富文本编辑器特殊调整 ([fa84d00](https://github.com/pure-admin/vue-pure-admin/commit/fa84d00a753d3eb4ba0fffb66c61021cb341c66c))
- 学历专业筛选及接口 ([7f9ee53](https://github.com/pure-admin/vue-pure-admin/commit/7f9ee53d4bf5e0e9b750897aa0a40a52a4baa6d9))
- 邀请参课 ([65097fb](https://github.com/pure-admin/vue-pure-admin/commit/65097fb6e2de396e786464dfddb0ed1f118851e2))
- 邀请管理和机构师资筛选项 ([f096035](https://github.com/pure-admin/vue-pure-admin/commit/f096035757f7178db57aec2e85734a6f281c3134))
- 邀请管理专家评价接口 ([e1e790f](https://github.com/pure-admin/vue-pure-admin/commit/e1e790fe0a6fe7949735a4d8ae4cc7e39ec8391f))
- 邀请结束常驻二次确认 ([70b2c97](https://github.com/pure-admin/vue-pure-admin/commit/70b2c97596012eff8c9deac68ffd49f9b2a59f81))
- 引入iconfont重构课程详情页面 ([9d94827](https://github.com/pure-admin/vue-pure-admin/commit/9d9482707df34fbb3d6b8afc6572785175c0bad8))
- 用户协议和注意事项材料说明默认值 ([e46c365](https://github.com/pure-admin/vue-pure-admin/commit/e46c3655da1cb0fc0754475b1e9f81c9db8ad129))
- 优化课程知识点模块交互及表单处理逻辑 ([894e7b1](https://github.com/pure-admin/vue-pure-admin/commit/894e7b141c46ff17317909a33913d988035af9ff))
- 优化知识点模块材料关联回显及表单处理逻辑 ([e8a059f](https://github.com/pure-admin/vue-pure-admin/commit/e8a059f8c6eca8fabc64b8371223e119b54a20f6))
- 优化知识点模块材料关联显示逻辑及表单交互处理 ([7ea79ed](https://github.com/pure-admin/vue-pure-admin/commit/7ea79ed4068a6fa61b991206c0c7741c9fd9340c))
- 优化专家信息图片展示样式 ([5e8af3c](https://github.com/pure-admin/vue-pure-admin/commit/5e8af3c5b43a0010a9f09be2ac83b4db6728b441))
- 知识点详情展示 ([2db7f2b](https://github.com/pure-admin/vue-pure-admin/commit/2db7f2b4810d0e3a6062c11a786680eda4ace42e))
- 重构课程详情页 ([694067c](https://github.com/pure-admin/vue-pure-admin/commit/694067cc6ee4b2437f8394d4029deb6a1d4c5279))
- 重构课程知识点模块及草稿箱功能 ([9fb219f](https://github.com/pure-admin/vue-pure-admin/commit/9fb219fb97bd7ae8dcec1e56cd550378c1f00539))
- 重构知识点模块材料关联功能，新增数据回显及验证逻辑 ([e9491d6](https://github.com/pure-admin/vue-pure-admin/commit/e9491d66c7f26c9939d530d52dec7dab9a6e7c09))
- 专家评价 ([ee619fc](https://github.com/pure-admin/vue-pure-admin/commit/ee619fc426314d66e459f7283c16435f948358d1))

### Bug Fixes

- 参与课程页面调整 ([ae24893](https://github.com/pure-admin/vue-pure-admin/commit/ae24893f3ef2f896daebdf6bc3c8fb8111f5da47))
- 参与课程页面调整 ([02c2a8a](https://github.com/pure-admin/vue-pure-admin/commit/02c2a8a55abee0a50a083ddb142afa20f0151412))
- 调整知识点表格列宽及样式，优化对话框标题和按钮布局 ([372ad4f](https://github.com/pure-admin/vue-pure-admin/commit/372ad4f5762cb90d22135e2de83873879ae800f4))
- 价格设置使用系统保留字拦截异常 ([3956d4c](https://github.com/pure-admin/vue-pure-admin/commit/3956d4cfd0db1aa8868b1be8af5329788643bdbf))
- 将"基地"统一修改为"实践点"，包括接口注释、路由配置、页面展示等 ([383db1a](https://github.com/pure-admin/vue-pure-admin/commit/383db1a1b28a386e9a200be05d80540b52abb2db))
- 开发环境还原 ([0719837](https://github.com/pure-admin/vue-pure-admin/commit/0719837796470cc0364fa7ac2d53e909ac346fc2))
- 开启团购/关闭团购改为课程定制/取消定制 ([a60b796](https://github.com/pure-admin/vue-pure-admin/commit/a60b796b9b842db6f54b0ab05bb9b25ce9dd7daa))
- 课程标签改为课程亮点标签 ([a93e706](https://github.com/pure-admin/vue-pure-admin/commit/a93e706bede807898c75adb7141b4a33dc8e1dd8))
- 课程简介及课程课期标签说明 ([a670e4b](https://github.com/pure-admin/vue-pure-admin/commit/a670e4b0d401ce86b6370193cf928c8bd2ac2036))
- 课程简介限制字数50 ([126e00a](https://github.com/pure-admin/vue-pure-admin/commit/126e00ad8acda8513238986db029445f02a347c8))
- 课程课期标签说明及审核后机构回复不能再次编辑 ([06c8360](https://github.com/pure-admin/vue-pure-admin/commit/06c8360ad76811dcbc72c0f6166d13a670bdaf3c))
- 课程详情页面细节调整 ([84a7e15](https://github.com/pure-admin/vue-pure-admin/commit/84a7e15b9b638f9de92b15c6d118e4c81b50038d))
- 课程知识点下一步报错问题 ([7a6c662](https://github.com/pure-admin/vue-pure-admin/commit/7a6c66247d3467ac5558378afcd92909b1362919))
- 课期编辑添加师资 ([aa6d3aa](https://github.com/pure-admin/vue-pure-admin/commit/aa6d3aa3b3925fbb00ce1a1d3c4fda47ccc52860))
- 课期状态筛选调整 ([cc33e27](https://github.com/pure-admin/vue-pure-admin/commit/cc33e27518270086a00dff676c7cbb8a9b0379a3))
- 领队讲师调整 ([54909b6](https://github.com/pure-admin/vue-pure-admin/commit/54909b665f29b6b6833b3a43c94063b679ddc9a1))
- 领队讲师显示问题 ([cc19bf4](https://github.com/pure-admin/vue-pure-admin/commit/cc19bf4bffbfcf472b4ad9710098622d8d59e178))
- 能力提升提行显示 ([2445940](https://github.com/pure-admin/vue-pure-admin/commit/2445940d0ad0ff5c9bef5e414c2fc80c0f38cd3d))
- 年龄比较强转为数字 ([d177721](https://github.com/pure-admin/vue-pure-admin/commit/d17772191ae41d96957e0853cfc0f4b1aeb6d068))
- 取消审核调整为撤销申请 ([bd7f45e](https://github.com/pure-admin/vue-pure-admin/commit/bd7f45eaa407efc2573a9c7aed18eb87e5486e3e))
- 上架申请文案调整 ([c9ca673](https://github.com/pure-admin/vue-pure-admin/commit/c9ca67391d2526aab3a99796ae398a5c60d11064))
- 师资模块首页加keep-alive及邀请参课路由跳转 ([808243e](https://github.com/pure-admin/vue-pure-admin/commit/808243e44d7ef879839d869fdd49d32ffb92a8cb))
- 师资相关代码屏蔽 ([515ec13](https://github.com/pure-admin/vue-pure-admin/commit/515ec132b33ad5313ab49df7addd21dccaaefd5b))
- 师资详情页调整 ([2f6864b](https://github.com/pure-admin/vue-pure-admin/commit/2f6864bd7e790deee9f1d4ad3c92a3001926bb23))
- 行程点默认值 ([818d475](https://github.com/pure-admin/vue-pure-admin/commit/818d4759ef563cc40c21af1daf8396e0f1be8647))
- 修复讲师管理跳转课程详情面包屑异常 ([a59cf92](https://github.com/pure-admin/vue-pure-admin/commit/a59cf92a9c2785025a69850ccf873762a08f5907))
- 选择课程组件缓存 ([037446a](https://github.com/pure-admin/vue-pure-admin/commit/037446a2bfe88efc5f918a6cf2ae64480920ea90))
- 选择课期背景调整 ([51680ce](https://github.com/pure-admin/vue-pure-admin/commit/51680ce946c630ae0524c988fe34d13a34d89277))
- 移除知识点模块中未使用的材料关联和核心知识点回显逻辑 ([b037d17](https://github.com/pure-admin/vue-pure-admin/commit/b037d17f4fe26079a54dde6b0b993489a557164d))
- 优化机构详情页图片展示样式和布局 ([6930e66](https://github.com/pure-admin/vue-pure-admin/commit/6930e6656d8e1ab513031ba0494c40cda5a398b7))
- 优化知识点保存逻辑 ([c067fcd](https://github.com/pure-admin/vue-pure-admin/commit/c067fcd709d90e393eb12891539f9d1a0d819a2f))
- 优化知识点模块材料关联懒加载逻辑及数据清空处理 ([2a24054](https://github.com/pure-admin/vue-pure-admin/commit/2a240545b5a1ff24b061b0b81265c846c6286cbc))
- 优化知识点模块教材关联逻辑，修复回显及表单处理问题 ([8a212d9](https://github.com/pure-admin/vue-pure-admin/commit/8a212d99646c0831c7e957897b316ca02b3e33d8))
- 装备说明改为材料说明 ([f7ba950](https://github.com/pure-admin/vue-pure-admin/commit/f7ba9503d998acbeeb1409a6f3f44e5cfe521faa))
- 作业改为实践感悟 ([6c04fc8](https://github.com/pure-admin/vue-pure-admin/commit/6c04fc81994704913dab50ec77f4befc24218570))
- ai工具地址 ([cce5427](https://github.com/pure-admin/vue-pure-admin/commit/cce5427ac7077d81fa9d21693f857cd74bc04b7a))

## [1.11.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.10.3...v1.11.0) (2025-07-25)

### Features

- 新增行程富文本字体相关特殊处理 ([bce6ed0](https://github.com/pure-admin/vue-pure-admin/commit/bce6ed03c776a638efc388bc93b1b27008f0a81a))

### Bug Fixes

- 保存草稿箱选了日期就要选开课时间 ([1fba780](https://github.com/pure-admin/vue-pure-admin/commit/1fba780777ebdc4e780195a2a6d9f72666d0360d))
- 复制课期可选择当日 ([a3018f0](https://github.com/pure-admin/vue-pure-admin/commit/a3018f01aed91496a79b7a7012ec861f7c9e1e35))
- 基础信息调整 ([511268d](https://github.com/pure-admin/vue-pure-admin/commit/511268d43bf2485fc7eaa3090934a0989765213c))
- 价格设置价格精度小数点后两位，数量为整数 ([c585c51](https://github.com/pure-admin/vue-pure-admin/commit/c585c5198c9bf47955b9f96cbdc56b0261994da7))
- 课程新建和编辑年龄限制输入0开头 ([d1bd398](https://github.com/pure-admin/vue-pure-admin/commit/d1bd398231710539a4c4dbbf1d43887702eb586c))
- 课期复制命名示意异常 ([10dc34b](https://github.com/pure-admin/vue-pure-admin/commit/10dc34b1703f017baa5a9c90fdf7aa49267d0329))
- 限制年龄段只能输入数字 ([f771846](https://github.com/pure-admin/vue-pure-admin/commit/f7718469457a924ed8903699e6feee7c7b0dcabb))
- 账号管理输入框中间去空及数字输入框禁用文字输入 ([14b6693](https://github.com/pure-admin/vue-pure-admin/commit/14b66932e988f46c96b4e8926f9a080f34885bd0))

### [1.10.3](https://github.com/pure-admin/vue-pure-admin/compare/v1.10.2...v1.10.3) (2025-07-17)

### Bug Fixes

- 侧边栏显示逻辑,更新首页信息展示 ([7321e25](https://github.com/pure-admin/vue-pure-admin/commit/7321e252b9250547fd71ea79d0a48edff787b612))
- 动态显示订单金额和退款金额标签 ([a472846](https://github.com/pure-admin/vue-pure-admin/commit/a47284642b0ddf168085a5f8f213f114338e0330))
- 机构编辑页面限制只能选择今天及之前的日期 ([67d8eed](https://github.com/pure-admin/vue-pure-admin/commit/67d8eedfb20ecf51a0a2723d952d93b2628acb21))
- 课堂跟踪时间调整及课程基本信息跳转删除存储信息 ([0039e8a](https://github.com/pure-admin/vue-pure-admin/commit/0039e8aa0fa104c993da37e5f31c919919f0b9f1))
- 完成无人数上限和年龄限制显示 ([461bdfe](https://github.com/pure-admin/vue-pure-admin/commit/461bdfea990b5de99bf2df90ca536aecc9438713))
- 修复机构管理页面图片显示逻辑 ([816e1e3](https://github.com/pure-admin/vue-pure-admin/commit/816e1e361dfaca69271cfcde63a3f2e1dc881ec6))
- 修复课程编辑基本信息图像上传失败问题 ([e2e91d6](https://github.com/pure-admin/vue-pure-admin/commit/e2e91d678ee6fcfe7006478e6bb7b4c4bde03b7e))
- 移除版本检测逻辑，简化应用初始化过程 ([7e3339d](https://github.com/pure-admin/vue-pure-admin/commit/7e3339db357a9ef4e4a151623d51d91fcb5776e0))
- 优化多页面图片上传功能，添加上传进度展示和错误处理 ([52386ce](https://github.com/pure-admin/vue-pure-admin/commit/52386ce02e4bc85768bcc953174c2e85eedd2a9c))
- 优化机构编辑页面表单验证规则 ([9fcfde6](https://github.com/pure-admin/vue-pure-admin/commit/9fcfde6f87d485e31aa0122b5aaa5f02f8bb9ca5))
- 优化课程封面和课期封面上传功能逻辑 ([55ea189](https://github.com/pure-admin/vue-pure-admin/commit/55ea189d336c63834cbbc804dd7f1cd92bfe4c8f))

### [1.10.2](https://github.com/pure-admin/vue-pure-admin/compare/v1.10.1...v1.10.2) (2025-07-17)

### [1.10.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.10.0...v1.10.1) (2025-07-09)

### Bug Fixes

- 调整登录页面logo样式 ([1bc2b65](https://github.com/pure-admin/vue-pure-admin/commit/1bc2b65e70b27f249419c4ef1f420a7d4ce2012f))
- 更新机构名称存储逻辑，优先使用localStorage中的值 ([b31c3b0](https://github.com/pure-admin/vue-pure-admin/commit/b31c3b0a70e8bb908aeef4cf7f3002f0b92180c7))
- 修改登录页面logo展示 ([4b28434](https://github.com/pure-admin/vue-pure-admin/commit/4b2843478d402469ed2408f3754a461b9d8b8d74))
- 优化订单对话框二维码参数配置，移除环境判断逻辑 ([4da999c](https://github.com/pure-admin/vue-pure-admin/commit/4da999c27fa94708e0bb3373edd1045e7ca7f4a8))
- 优化课程编辑页面图片上传功能 ([dbebd52](https://github.com/pure-admin/vue-pure-admin/commit/dbebd521dc897635a393d6c23ce355af98ee63a0))
- 优化课程封面编辑逻辑 ([70f5065](https://github.com/pure-admin/vue-pure-admin/commit/70f50651dcd3f457e8611f17f6125a437802ae13))

## [1.10.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.9.0...v1.10.0) (2025-07-09)

### Features

- 添加AI课程设计侧边栏 ([387f8c3](https://github.com/pure-admin/vue-pure-admin/commit/387f8c3ac5164df54b595a24aa7f6f32457846e0))

### Bug Fixes

- 机构编辑增加重复文件检查 ([ec0b5db](https://github.com/pure-admin/vue-pure-admin/commit/ec0b5db145ba5a0d9dd74d98828c16640fd42b45))
- 基础信息自动保存 ([e21e1fc](https://github.com/pure-admin/vue-pure-admin/commit/e21e1fc6ce57f6086b56ec4f9a99d45a1f7a5abc))
- 课程复制接口数据调整 ([cb8790d](https://github.com/pure-admin/vue-pure-admin/commit/cb8790d6a3b4bafa9967895da8e655dd91917073))
- 领队讲师新增编辑邮箱校验 ([be8c080](https://github.com/pure-admin/vue-pure-admin/commit/be8c080dc0f24c3658415f046acff49b50da8f94))
- 评价评分筛选优化 ([8cbff08](https://github.com/pure-admin/vue-pure-admin/commit/8cbff0892b889ac720eaf8dc533b93cd7a5e86e6))
- 修复订单管理退款状态展示异常 ([26cb823](https://github.com/pure-admin/vue-pure-admin/commit/26cb823b9e00d0c8ba2eb2108f676ed8b1350388))
- 修改ai助手文案为ai课程设计 ([aa72f10](https://github.com/pure-admin/vue-pure-admin/commit/aa72f103c4504b27b77523e71261b7aeeebebcf2))
- 优化机构编辑页面电话号码校验规则并添加地址选择框只读属性 ([ad8c2d4](https://github.com/pure-admin/vue-pure-admin/commit/ad8c2d4cc42827c647bb6ba0ff4d4893aa4949dd))
- 优化机构编辑页面bug ([c3e6b25](https://github.com/pure-admin/vue-pure-admin/commit/c3e6b251d4da34fa0d45b6b9c571ff1e2f9df2b6))

## [1.9.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.8.0...v1.9.0) (2025-07-03)

### Features

- 机构编辑页面表单优化及上传功能增强 ([38de896](https://github.com/pure-admin/vue-pure-admin/commit/38de896b11819eb598360f9cb0f12aa8d0d58cf3))
- 机构编辑页面新增营业执照及资质文件校验功能 ([8c2ab60](https://github.com/pure-admin/vue-pure-admin/commit/8c2ab60f3dbfa1f6b819e8c6ccc14cae2da3544b))

### Bug Fixes

- 草稿箱侧边栏bug修改 ([b96a15b](https://github.com/pure-admin/vue-pure-admin/commit/b96a15b66c5ed0cd11297b4d29bc75d33ef65c8c))
- 草稿箱整理+课期行程自动保存 ([3570fc0](https://github.com/pure-admin/vue-pure-admin/commit/3570fc0d58113426046d8dce208c4accc95ae77e))
- 创建账号报错文案显示在行内 ([84b8b03](https://github.com/pure-admin/vue-pure-admin/commit/84b8b03db51750cbe9db8f34d116972ea3114238))
- 地图组件标题 ([2676fe9](https://github.com/pure-admin/vue-pure-admin/commit/2676fe915a1afa4eccb36aa9f205f553f1f52a56))
- 价格设置价格限制99999 ([13bc4f0](https://github.com/pure-admin/vue-pure-admin/commit/13bc4f070d6175aeca6c8d81ff1830ecaeb042d9))
- 上传图片样式处理 ([422bfbd](https://github.com/pure-admin/vue-pure-admin/commit/422bfbd1d4a165e1b7f012ace74faceb0fc0b684))
- 修复机构编辑页面图片预览功能 ([fe3b5f6](https://github.com/pure-admin/vue-pure-admin/commit/fe3b5f67e08a1ce043ec813c7e4ef7bea8e0bea9))
- 修改基地介绍校验逻辑，优化HTML标签处理及空值检查 ([894aa7d](https://github.com/pure-admin/vue-pure-admin/commit/894aa7d82b04d438008ad6d668b2e45289e3fb22))
- 修正机构编辑页面图片上传说明文字 ([5e3fcfe](https://github.com/pure-admin/vue-pure-admin/commit/5e3fcfe7e76992a8124000cf90d3a5164ab9cb11))
- 修正机构编辑页面图片上传最佳尺寸说明文字 ([bc53ba5](https://github.com/pure-admin/vue-pure-admin/commit/bc53ba5c22eb2419bb149a3fa8ac5e95e82250bd))
- 优化机构编辑页面 ([7941df1](https://github.com/pure-admin/vue-pure-admin/commit/7941df19c50605d503b695c9d19ad0cb8a1a6d05))
- 优化微信绑定逻辑，避免重复处理请求 ([46488f3](https://github.com/pure-admin/vue-pure-admin/commit/46488f3510bc6ede7f5bbe103412e207d537035d))
- 优化微信解绑逻辑，增加防抖处理并更新操作文案 ([d7468f2](https://github.com/pure-admin/vue-pure-admin/commit/d7468f25a13a331144f0f6b649fd35a0e28529ff))
- 账号编辑中身份证号报错文案在行内显示 ([bca9352](https://github.com/pure-admin/vue-pure-admin/commit/bca9352f4c1359ce8ba657815613a1bcb2df84b2))

## [1.8.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.7.0...v1.8.0) (2025-07-02)

### Features

- 财务管理金额新增￥ ([a992ff2](https://github.com/pure-admin/vue-pure-admin/commit/a992ff2784264c17037a3d251438314b37f967d1))
- 二维码 ([9b8baaf](https://github.com/pure-admin/vue-pure-admin/commit/9b8baaf9435d7b6ebbfc185af4d3c6d835f60646))
- 二维码登录以及绑定微信 ([3f1b94e](https://github.com/pure-admin/vue-pure-admin/commit/3f1b94e5eef74fed2114f98826de04b3dffbe475))
- 合并 ([279e77b](https://github.com/pure-admin/vue-pure-admin/commit/279e77b2dea0722e300f046853a557e43f492324))
- 机构编辑页面表单类型调整 ([759cbbd](https://github.com/pure-admin/vue-pure-admin/commit/759cbbda30e6db2c8970fd68664317c3385eaf0b))
- 机构编辑页面功能增强及表单验证完善 ([11b6c1d](https://github.com/pure-admin/vue-pure-admin/commit/11b6c1da012e3f9ce09dc69f3f47335d0e440745))
- 解绑 ([eb050c1](https://github.com/pure-admin/vue-pure-admin/commit/eb050c16945e6041a8c45092c67625557ad9e892))
- 解绑以及日志 ([f4f4a02](https://github.com/pure-admin/vue-pure-admin/commit/f4f4a02776bac6511fb58dc49477120306d31379))
- 优化操作日志描述及图片标签格式 ([d4d1896](https://github.com/pure-admin/vue-pure-admin/commit/d4d18968f8b5d28ceea38ddbc041eaa225dff96a))
- 增强地图功能，支持用户位置获取及选择位置的持久化 ([1baf12b](https://github.com/pure-admin/vue-pure-admin/commit/1baf12bc2a6d3250fcb4769ee7b4135e7a9e9243))

### Bug Fixes

- 绑定调整 ([a0b5d94](https://github.com/pure-admin/vue-pure-admin/commit/a0b5d9443af701e6f7acba71bca36b8e949a7ad5))
- 绑定调整 ([dccfd54](https://github.com/pure-admin/vue-pure-admin/commit/dccfd5425774ff034d07b8dafc16af4788fcf641))
- 绑定调整 ([5f1b8e8](https://github.com/pure-admin/vue-pure-admin/commit/5f1b8e8963680d3d72f563254e7c395e9cba717e))
- 绑定提示调整 ([376a860](https://github.com/pure-admin/vue-pure-admin/commit/376a8605e867520e80c8aa4d4f4cb8cf30b93f3b))
- 保存基础信息问题 ([be782be](https://github.com/pure-admin/vue-pure-admin/commit/be782be16e72345106ec1c4f0908f24c10d93ee2))
- 编辑课程信息人数上限限制及领队讲师限制报错在侧边 ([461b00b](https://github.com/pure-admin/vue-pure-admin/commit/461b00bbcae8a9db5460f06044304016125d64b5))
- 补充state校验 ([7d4218b](https://github.com/pure-admin/vue-pure-admin/commit/7d4218ba9d0c0b4384cc62a8a004713242f33901))
- 财务管理页面添加导出确认弹窗及清除时间选择功能 ([5aa13ba](https://github.com/pure-admin/vue-pure-admin/commit/5aa13baf8ec5e669a57ee6f1fdd13b54d43ccfff))
- 财务管理页面支付金额添加人民币符号 ([3cfcc9b](https://github.com/pure-admin/vue-pure-admin/commit/3cfcc9bf117cb1712baf365d8b8f2e6c97a8cb4d))
- 草稿及编辑开启使用封面按钮调整 ([28c9d4f](https://github.com/pure-admin/vue-pure-admin/commit/28c9d4f421769e6af0317bd7244ae6a89d5a5d99))
- 草稿箱侧边栏点击调整 ([14401f5](https://github.com/pure-admin/vue-pure-admin/commit/14401f53bfd0c748d95e9952409062e00de2e2b2))
- 草稿箱侧边栏防抖 ([0a404a1](https://github.com/pure-admin/vue-pure-admin/commit/0a404a16ebd024b25c39bbb1dcda7db0e1f63ef8))
- 草稿箱基础信息人数上限限制及草稿箱基础信息图片调整 ([783e288](https://github.com/pure-admin/vue-pure-admin/commit/783e2884047c2bb79045eeff40f4b9c705a78177))
- 草稿箱列表完成度添加文字提示 ([406b78b](https://github.com/pure-admin/vue-pure-admin/commit/406b78b5b6a60ac7f79d08d88971aea6fe7651c8))
- 草稿箱路由调整 ([514f6f6](https://github.com/pure-admin/vue-pure-admin/commit/514f6f6cc140c1ec038e0c468ef161c98d7da553))
- 草稿箱删除日志描述调整 ([45b2eec](https://github.com/pure-admin/vue-pure-admin/commit/45b2eec7e2e4e5b64bc278170f8f28dcb797f987))
- 草稿箱填写说明bug ([3a09bb9](https://github.com/pure-admin/vue-pure-admin/commit/3a09bb9f88788ddf2c925a549bc539ff8ffaaa99))
- 草稿箱完成的基地的展示 ([448a096](https://github.com/pure-admin/vue-pure-admin/commit/448a0961b8bc125455da79fc70c164da627e2797))
- 草稿箱完成的课期名的展示 ([5db53f2](https://github.com/pure-admin/vue-pure-admin/commit/5db53f22f793aa402eac1b5c80746bb3739a55d7))
- 草稿箱完成UI调整 ([5811bb0](https://github.com/pure-admin/vue-pure-admin/commit/5811bb0518fd0f473a527668ab2b425bec9506af))
- 侧边栏点击注意事项bug ([680ea10](https://github.com/pure-admin/vue-pure-admin/commit/680ea10196863e082b336f002d7016a44dd619cb))
- 侧边栏切换页面,保存数据2.0 ([7f38081](https://github.com/pure-admin/vue-pure-admin/commit/7f3808123861b916f2d4435687a57ec9c40fbae6))
- 侧边栏切换页面,数据保存 ([ac95a02](https://github.com/pure-admin/vue-pure-admin/commit/ac95a02f246f08d5aa4e689b75e00f3c8b93bac3))
- 冲突合并 ([34149e5](https://github.com/pure-admin/vue-pure-admin/commit/34149e500d88f6bb29f2c6316a7def15c8944e80))
- 错误状态码处理 ([edd072b](https://github.com/pure-admin/vue-pure-admin/commit/edd072bf7ddd0ac92eecde77dac8aa424d6b597b))
- 登录多用户调整 ([738cd35](https://github.com/pure-admin/vue-pure-admin/commit/738cd3536f83815b8655896297ad7b3a30d9517e))
- 调整扫码登录 ([fc26228](https://github.com/pure-admin/vue-pure-admin/commit/fc2622869c2012da450214b4dc9ba5adf92b2ed1))
- 订单管理、领队讲师管理搜索框去空格 ([54c58d1](https://github.com/pure-admin/vue-pure-admin/commit/54c58d1ed413bdcbe555cc639e9b021a989e1146))
- 多余参数导致面包屑生成问题 ([4ac3502](https://github.com/pure-admin/vue-pure-admin/commit/4ac3502e31b9781bc7ad156c594b1925ab8aa90f))
- 二维码调整 ([6d84335](https://github.com/pure-admin/vue-pure-admin/commit/6d84335bea7388fc55429f19bdbab361313ee6bf))
- 更新订单文件操作日志描述 ([e0925b0](https://github.com/pure-admin/vue-pure-admin/commit/e0925b0c38732f59e1cc6e799abdd69cb5591f4d))
- 更新分页组件的页面大小选项和默认大小 ([715b45a](https://github.com/pure-admin/vue-pure-admin/commit/715b45a85262484e05b9bd0ce1ea65e44314bfd4))
- 更新关联课程跳转路由 ([76d6c17](https://github.com/pure-admin/vue-pure-admin/commit/76d6c174d41a4350c19a9e181dff0800b6fc0ab7))
- 更新异步任务处理逻辑，优化取消操作 ([e6cc14f](https://github.com/pure-admin/vue-pure-admin/commit/e6cc14fc0b0a5a372713f554238306bdafa0ab65))
- 合并 ([38b7bd6](https://github.com/pure-admin/vue-pure-admin/commit/38b7bd67825f6d9b16b747ea3d88b7ee6e635918))
- 价格设置调整 ([7243219](https://github.com/pure-admin/vue-pure-admin/commit/724321981272dbe50d3eebb9c3cafc634265a4ac))
- 价格设置高度问题，空白区域过大 ([5ed45cb](https://github.com/pure-admin/vue-pure-admin/commit/5ed45cbc61be85581432730b7e6d9cdceffc0a88))
- 价格设置规格长度及价格最大值设置（bug991） ([88b2e96](https://github.com/pure-admin/vue-pure-admin/commit/88b2e961fd4f8b1218c12e115cc637f0e0d6ddfc))
- 课程编辑最小年龄最大年龄填写调整（bug919） ([3a7a65f](https://github.com/pure-admin/vue-pure-admin/commit/3a7a65f086eb5df11740808dadf8204be7b4bbf5))
- 课程评价人数上限改为课程标签 ([b2936f3](https://github.com/pure-admin/vue-pure-admin/commit/b2936f3c77ab42c9140e7795fbed46a64a129702))
- 课程相关页面统一分页器 ([b4dddd4](https://github.com/pure-admin/vue-pure-admin/commit/b4dddd405c68d388756200a93565fdd6947d5ef6))
- 课期报告调整 ([682f31f](https://github.com/pure-admin/vue-pure-admin/commit/682f31fc6281666a676166585586b4314d92173a))
- 课期行程智能填充行程内容异步接口优化 ([16295ef](https://github.com/pure-admin/vue-pure-admin/commit/16295ef9af075fca2ef2d20db29d11ea13c3749d))
- 课期行程UI调整 ([ad0fe58](https://github.com/pure-admin/vue-pure-admin/commit/ad0fe58b1d2a2cf0da65840d307a22985bac1cc2))
- 快捷登录 和 路由刷新 ([a6fc096](https://github.com/pure-admin/vue-pure-admin/commit/a6fc096702151fbadf2248e7e9834bf9a0810a10))
- 路由调整 ([df7178f](https://github.com/pure-admin/vue-pure-admin/commit/df7178ff6b8ab46e7b88a1b4c97d433024f6931c))
- 路由跳转调试 ([975dd3e](https://github.com/pure-admin/vue-pure-admin/commit/975dd3e7557a5bcbb0cc791653ee4ce576591073))
- 清除路由参数移除 ([f91e3bd](https://github.com/pure-admin/vue-pure-admin/commit/f91e3bd689d5d10f5689b2977897ce45d77ad069))
- 扫码问题 ([cec07ee](https://github.com/pure-admin/vue-pure-admin/commit/cec07eefe07fa01c98ed89c59429fcf50317f557))
- 删除基地失败提示文案修改 ([d76557d](https://github.com/pure-admin/vue-pure-admin/commit/d76557d9d40e76aca63fefae6c91a6d89347b4ac))
- 首页编辑信息身份证提示位置 ([65ffee9](https://github.com/pure-admin/vue-pure-admin/commit/65ffee94e6b286445f0c47130f80312e8e500314))
- 首页修改密码优化 ([f39c5a5](https://github.com/pure-admin/vue-pure-admin/commit/f39c5a58b2067f3cbcb5381c1697dde3a5b90734))
- 文件上传限制统一调整 ([7d1679b](https://github.com/pure-admin/vue-pure-admin/commit/7d1679b105d56962b03080c4cf565d5daedc505b))
- 详情绑定跳转路由补充参数 ([276db2d](https://github.com/pure-admin/vue-pure-admin/commit/276db2d05d71d8f6bba75e56204943b4859330b2))
- 校验 state调整 ([2adf206](https://github.com/pure-admin/vue-pure-admin/commit/2adf206cdb4a9080d2846964ce225e39b176fafc))
- 新建基地基地联系电话格式验证 ([781f669](https://github.com/pure-admin/vue-pure-admin/commit/781f669346f425b2b1517b467ecab5e7df78f209))
- 新建领队讲师输入框限制 ([f2005c7](https://github.com/pure-admin/vue-pure-admin/commit/f2005c77eafb5a0c1b65ee824fdf9af723c3d435))
- 修复编辑页面邮箱校验文案样式异常 ([18153fd](https://github.com/pure-admin/vue-pure-admin/commit/18153fd35e7c1d758638a500cfaa670994d8a845))
- 修复编辑账号面包屑，账号信息文案 ([db21a5d](https://github.com/pure-admin/vue-pure-admin/commit/db21a5d218ebd4562374ad25e43f49b9c77422c7))
- 修复操作视频面包屑异常 ([7b946d7](https://github.com/pure-admin/vue-pure-admin/commit/7b946d70b5c138f91e64afc4c8287c75cd3d2e21))
- 修复登录组件中loading状态异常 ([9bb4fa4](https://github.com/pure-admin/vue-pure-admin/commit/9bb4fa4616c9fd746fa360cba51ff97b8f360cd9))
- 修复订单、财务管理重置功能异常 ([fff6122](https://github.com/pure-admin/vue-pure-admin/commit/fff61226c6cd8e2085a38968f3aee44da93e10c0))
- 修复订单管理路由异常 ([f19bb00](https://github.com/pure-admin/vue-pure-admin/commit/f19bb0083faae45edca3478db87cccba4613bc3c))
- 修复订单管理面包屑异常 ([edfd12a](https://github.com/pure-admin/vue-pure-admin/commit/edfd12a6e5dbf974881b1343c178375820fbcd3d))
- 修复团购订单数据重复展示异常 ([cc3234d](https://github.com/pure-admin/vue-pure-admin/commit/cc3234d08d0a2484fad0f6d40a6c10901ca68030))
- 修改财务金额展示 ([24ab79c](https://github.com/pure-admin/vue-pure-admin/commit/24ab79c8b2d5a89a4731a8ce9bc1bbc21c8ee949))
- 修改订单上传文件文案 ([e801c71](https://github.com/pure-admin/vue-pure-admin/commit/e801c710e08b49c7960b631eedc2c8bcb6f8ed17))
- 修改客服热线正则 ([5b8a385](https://github.com/pure-admin/vue-pure-admin/commit/5b8a3858a60af7e1af1c9d6f67d9c19c712e7619))
- 修改身份证验证规则 ([2c8fd0f](https://github.com/pure-admin/vue-pure-admin/commit/2c8fd0f28a0a37c39aa2a5976f1342d1de863725))
- 修改微信登录相关日志文案，调整code码获取方式 ([7b4935b](https://github.com/pure-admin/vue-pure-admin/commit/7b4935b0cd4fc0b2443c1915195b919ef04dd2ba))
- 修改账号编辑样式 ([711db43](https://github.com/pure-admin/vue-pure-admin/commit/711db439361cb9f13e00697b0bc77fa65f7cdc2e))
- 选择基地地址项禁用tab切换下一项 ([702b522](https://github.com/pure-admin/vue-pure-admin/commit/702b5222f9e997d37e1fc99858132c7e909b954c))
- 一直loading处理 ([ccd3e81](https://github.com/pure-admin/vue-pure-admin/commit/ccd3e8111dda85c5ddeb7038d1336bf0be50a0ce))
- 移除 部分提示 ([f13d6bd](https://github.com/pure-admin/vue-pure-admin/commit/f13d6bdd5a141bc1667d3a9dbb8aaf090638409c))
- 移除深色模式样式 ([0ef72d8](https://github.com/pure-admin/vue-pure-admin/commit/0ef72d8df0ae7cdbabebb15f368e0cd370067475))
- 用户评价调整 ([17bf575](https://github.com/pure-admin/vue-pure-admin/commit/17bf5755696a3eee732f2495109d520b306553bc))
- 优化账号编辑和教师详情页面样式，调整微信绑定提示文案 ([6e55a21](https://github.com/pure-admin/vue-pure-admin/commit/6e55a21989bb62947d6a3df052004d1cb05a7cac))
- 优化账号编辑页面样式和微信解绑提示文案 ([532aeb9](https://github.com/pure-admin/vue-pure-admin/commit/532aeb9ac4a262c90319890b5b678ae846ca802f))
- 优化重置和清除时间逻辑 ([adf95b2](https://github.com/pure-admin/vue-pure-admin/commit/adf95b2152cbfc156ca1f0a8df89d42d3422eae5))
- 账户管理重定向参数 ([fe86570](https://github.com/pure-admin/vue-pure-admin/commit/fe86570df8b9c674bd724aac8d83b4c2fa31dc9a))
- 智能填充遮罩层 ([edc79e8](https://github.com/pure-admin/vue-pure-admin/commit/edc79e820e764d60ba0a74ffe9ab4dab953ff21c))
- 智能填充遮罩层2.0 ([46619d8](https://github.com/pure-admin/vue-pure-admin/commit/46619d843c82cac37c883c75a15b408b8689867e))
- 重置课程未选择全部状态 ([f7c4cc1](https://github.com/pure-admin/vue-pure-admin/commit/f7c4cc1ba1ef8c336e03756105c47a21d21ca373))
- ai工具域名地址调整 ([1a6f4d2](https://github.com/pure-admin/vue-pure-admin/commit/1a6f4d2c2dbdfd04258ec6c9e824fb81a0678995))
- loading调整 ([893d37f](https://github.com/pure-admin/vue-pure-admin/commit/893d37f3d8a7d10de2777b888566318fa56dc7e6))
- state状态问题 ([01329b2](https://github.com/pure-admin/vue-pure-admin/commit/01329b2046c4d26a78979c6df9cfd3f9abefcf8d))

## [1.7.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.6.1...v1.7.0) (2025-06-20)

### Features

- 扫码登录部分调整 ([8ee9449](https://github.com/pure-admin/vue-pure-admin/commit/8ee944956ebfbae5deeecdd956d9f58699ba31be))

### Bug Fixes

- 绑定状态调整 ([b7bb735](https://github.com/pure-admin/vue-pure-admin/commit/b7bb735deaa320074c0313914e3ecaa0eaae87f3))
- 保存使用课程封面图状态 ([3b15e1c](https://github.com/pure-admin/vue-pure-admin/commit/3b15e1ca5342567c8f9cfea3dbd0d47ebf7e2b82))
- 编辑页和草稿及课程编辑标签去重、去空、字数限制 ([46010b7](https://github.com/pure-admin/vue-pure-admin/commit/46010b704cbee302f4304e34e877b485b627c6a1))
- 草稿箱-完成-课程基础信息+第几天(bug) ([3379cf7](https://github.com/pure-admin/vue-pure-admin/commit/3379cf734bbf0f52c3fa5f3946bde3e6a60e99e3))
- 草稿箱标签优化 ([3670509](https://github.com/pure-admin/vue-pure-admin/commit/3670509ba6a5f0bcabea73811a38b616e7204580))
- 草稿箱侧边栏按钮切换 ([3583b07](https://github.com/pure-admin/vue-pure-admin/commit/3583b07b062a98e1af17de618a2b80730fe0a1f0))
- 草稿箱侧边栏按钮切换-限制 ([264fe96](https://github.com/pure-admin/vue-pure-admin/commit/264fe96db1c99bdf8ef6552b91c4966ce8906afb))
- 草稿箱的完成新建按钮优化 ([25761bd](https://github.com/pure-admin/vue-pure-admin/commit/25761bd95c2d3569e5cc87d89366b2844fb23b83))
- 草稿箱基础信息UI调整 ([2218201](https://github.com/pure-admin/vue-pure-admin/commit/22182015673de62129d574d8f71a125dd4eb4b15))
- 草稿箱及编辑页基地领队讲师删除后回显异常问题 ([1f65435](https://github.com/pure-admin/vue-pure-admin/commit/1f654357124ced8d8a350361a4ce76cc5357ee6f))
- 草稿箱列表优化 ([427272a](https://github.com/pure-admin/vue-pure-admin/commit/427272a0e61d24330220ab4125a18a149ea1f97f))
- 草稿箱完成基地展示bug ([4c8cca1](https://github.com/pure-admin/vue-pure-admin/commit/4c8cca1f7182b4cced6d1dc88f50f697934173ed))
- 草稿箱智能填充优化 ([e95fba1](https://github.com/pure-admin/vue-pure-admin/commit/e95fba1e05649bf68bcd4777747051d8f8541aff))
- 关闭上传图片加载转圈效果 ([70e2210](https://github.com/pure-admin/vue-pure-admin/commit/70e2210313f5864da87be092102b7935e8b4b72d))
- 基础信息多余代码删除及作业设计store引入 ([693567a](https://github.com/pure-admin/vue-pure-admin/commit/693567a1f083f0612ae53e0bc804e95937434e77))
- 课程编辑上传限制9张后不显示上传按钮 ([013bb7a](https://github.com/pure-admin/vue-pure-admin/commit/013bb7aba3600d124d20348c5761c9ec4de12f4b))
- 课程列表页操作栏与左侧要预留间隙 ([c3cdd70](https://github.com/pure-admin/vue-pure-admin/commit/c3cdd70a6ac9ce081cbbdc115c8bbfa7926210a3))
- 课程首页表格课程状态代码调整 ([58e42d8](https://github.com/pure-admin/vue-pure-admin/commit/58e42d8392179df5e40e16b6dad4febd97548a54))
- 课期复制-添加-isCourseCover ([2ce820d](https://github.com/pure-admin/vue-pure-admin/commit/2ce820d70e97936e434ad2282685a084ea765b08))
- 图片上传了九张后不显示上传按钮 ([c49e5ec](https://github.com/pure-admin/vue-pure-admin/commit/c49e5ec0d747a76e22ea882428b655c8db510bb7))
- 微信绑定状态修复 ([6fe032b](https://github.com/pure-admin/vue-pure-admin/commit/6fe032b510a9cd66f3480fca0b644e592a10882e))
- 修复空课程标签的bug+代码bug ([b798d66](https://github.com/pure-admin/vue-pure-admin/commit/b798d664571d1454306f5cdb97c162c30bf51e7a))
- 修改财务管理页面默认排序字段 ([34be7f6](https://github.com/pure-admin/vue-pure-admin/commit/34be7f6ee6c5f9b66107f2ca76a7fb61dcee8717))
- 修改拦截器逻辑 ([ef7adf8](https://github.com/pure-admin/vue-pure-admin/commit/ef7adf87ec14290511cf7e4c33780ea7a327acef))
- 修改身份号字段标签为身份证号码 ([c780519](https://github.com/pure-admin/vue-pure-admin/commit/c780519cc9f8865429d9b81ae95b2118d009f27c))
- 移除无用验证码 ([88ccce8](https://github.com/pure-admin/vue-pure-admin/commit/88ccce863a8c52261422e8f97adebd2e2d8332e9))
- 优化财务管理页面表格 ([3243139](https://github.com/pure-admin/vue-pure-admin/commit/3243139f05773929f4d7cad806d98fef56ae1910))
- 注释掉财务管理页面keepAlive配置 ([1e5fdfe](https://github.com/pure-admin/vue-pure-admin/commit/1e5fdfef6d4e22ccc0b0d169a5073031d47b4522))
- 注释掉可提现金额统计项 ([c73d06c](https://github.com/pure-admin/vue-pure-admin/commit/c73d06c1a14bfab9a595a3a5537887a61aaf7c63))
- 最大年龄和最小年龄提示问题 ([13a19ef](https://github.com/pure-admin/vue-pure-admin/commit/13a19efdb0476ee255591d1e596c79d3c82b3ac1))

### [1.6.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.6.0...v1.6.1) (2025-06-20)

## [1.6.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.5.1...v1.6.0) (2025-06-17)

### Features

- 倒计时指令 ([397be87](https://github.com/pure-admin/vue-pure-admin/commit/397be8769bf026fea55b0063ca7dc30bb0aacf90))

### Bug Fixes

- 按钮不展示bug回调 ([a598f23](https://github.com/pure-admin/vue-pure-admin/commit/a598f23ba6e7161bb4e4ed922f2c926ff2f1f634))
- 草稿箱-完成-封面图bug ([ae0af32](https://github.com/pure-admin/vue-pure-admin/commit/ae0af32aacc93e0cee1eb809ab7e3f6f551a70ae))
- 草稿箱-完成-课程标签bug ([43a8852](https://github.com/pure-admin/vue-pure-admin/commit/43a8852178699969a4d16eef7513c67cad898bc1))
- 草稿箱侧边栏鼠标改小手 ([8e68b85](https://github.com/pure-admin/vue-pure-admin/commit/8e68b85e4ce1aa0349ae5beee4e3fea254020905))
- 地图预览功能功能 样式 调整 ([4895220](https://github.com/pure-admin/vue-pure-admin/commit/4895220ecacee727da97f89dbbae8619271aa91a))
- 富文本编辑器粘贴重复问题 ([cd1866f](https://github.com/pure-admin/vue-pure-admin/commit/cd1866fb4c5e31b46d21ef288aa71f590fb612c5))
- 价格设置数量价格回显问题 ([0888121](https://github.com/pure-admin/vue-pure-admin/commit/0888121b46f91416fd6c5c5184cdb5933ea2e96f))
- 角色管理弹窗禁用esc ([775e8d9](https://github.com/pure-admin/vue-pure-admin/commit/775e8d9fdb4f4288612719b947d49d529d065817))
- 课程标签调整 ([aaafd2b](https://github.com/pure-admin/vue-pure-admin/commit/aaafd2bf4119f36e58468253e046ffaedcd1660c))
- 课程类型上传参数调整 ([dc10a0c](https://github.com/pure-admin/vue-pure-admin/commit/dc10a0c3c790e439736db866f2846b38ebc7bad4))
- 课程首页横向滚动问题 ([765dcec](https://github.com/pure-admin/vue-pure-admin/commit/765dceccd5e3167dadd61041e7cc667427f4ba33))
- 课期编辑标签调整 ([fba4309](https://github.com/pure-admin/vue-pure-admin/commit/fba4309c0d00b16ea4e7fedcd0f307c6ee16d6a3))
- 课期复制前后缀选项调整 ([d41c0a0](https://github.com/pure-admin/vue-pure-admin/commit/d41c0a074b32f0eb43aacb8d8ef91b8fed5fcef0))
- 离开页面清除draftId ([455cf97](https://github.com/pure-admin/vue-pure-admin/commit/455cf971ddcfea83d905e49f29f42242b5e3ea1d))
- 验证码按钮补充倒计时 ([9a0f80b](https://github.com/pure-admin/vue-pure-admin/commit/9a0f80baa92a895fc095616b0f30501b952e74d7))
- 移除最小年龄校验逻辑 ([5ef6d98](https://github.com/pure-admin/vue-pure-admin/commit/5ef6d98c0dbeed183981580708b10aff755466f1))
- 邮箱身份证数据重置修复 ([6612ff3](https://github.com/pure-admin/vue-pure-admin/commit/6612ff32847b40c63a2c8bbbac33ee4e0ac8f124))
- 余额字段修改 ([3efca65](https://github.com/pure-admin/vue-pure-admin/commit/3efca65a264ba803e55e7c9a40ccd7e0973c102e))
- 智能填充确认bug+提示 ([a6b3b1a](https://github.com/pure-admin/vue-pure-admin/commit/a6b3b1af4d08e3b0f5b5f082198914a124eb64c4))
- 智能填充提示 ([9fd92e1](https://github.com/pure-admin/vue-pure-admin/commit/9fd92e14d16b1142c7f6d9ecb78b1a90fcc33356))
- 智能填充行程内容 ([f6d9b41](https://github.com/pure-admin/vue-pure-admin/commit/f6d9b41a5d50f2fde1f9687c05e6689308bc422c))
- 作业设计和价格设置获取存储draftId ([1e0be5b](https://github.com/pure-admin/vue-pure-admin/commit/1e0be5b15d9c63b9881db0b720cb65e0bfec4df0))
- ai工具方法调整 ([859a4d2](https://github.com/pure-admin/vue-pure-admin/commit/859a4d282a50a61f9f4b11cba8b277e8850da854))
- store存储draftId ([09ef12f](https://github.com/pure-admin/vue-pure-admin/commit/09ef12fd81517947f9666ecc7139f8c3d93661c9))

### [1.5.1](https://github.com/pure-admin/vue-pure-admin/compare/v1.5.0...v1.5.1) (2025-06-16)

### Bug Fixes

- 财务管理显示优化 ([1550644](https://github.com/pure-admin/vue-pure-admin/commit/155064457ee1811573b1c81077d10559c3d9dd63))
- 草稿箱图标间距 ([7121de3](https://github.com/pure-admin/vue-pure-admin/commit/7121de3bdd829f241a06179114c5611f4b536796))
- 课程财务页面数据展示优化为统计组件 ([f7c9096](https://github.com/pure-admin/vue-pure-admin/commit/f7c909624c609e76c76f22d8ce713784c691a65b))
- 图片最佳尺寸文案调整及课期编辑图片删除问题及年龄限制调整 ([fb58e8f](https://github.com/pure-admin/vue-pure-admin/commit/fb58e8fe2c9f252523cbf304a797446c17dc294c))
- 退出按钮外层有边框问题 ([a6b3634](https://github.com/pure-admin/vue-pure-admin/commit/a6b3634a1c1cb3b90ff47b477d654262b5eb3b0a))
- 修正课程管理页面按钮间距问题 ([fe293f6](https://github.com/pure-admin/vue-pure-admin/commit/fe293f6e93c2b807779aaac8b57e08476705d11e))
- 智能填充行程内容接口 ([bfb5312](https://github.com/pure-admin/vue-pure-admin/commit/bfb531261efde55fcd9ce352a28f830311221625))

## [1.5.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.4.0...v1.5.0) (2025-06-13)

### Features

- 补充地图功能 ([068c0ac](https://github.com/pure-admin/vue-pure-admin/commit/068c0ac5a02501339c1e29ec958079c4b2fe00fa))
- 补充接口经纬度 ([b1d91ce](https://github.com/pure-admin/vue-pure-admin/commit/b1d91ce5ec28a29af6429608ba7e64e8b19919cb))
- 基本信息保存接口 ([e99feab](https://github.com/pure-admin/vue-pure-admin/commit/e99feab4a414135275fa7de977cee0ce4942c6ee))
- 基础信息退出 ([40f9e61](https://github.com/pure-admin/vue-pure-admin/commit/40f9e61f30a5664fd84426aa6e4b48d1f3bca6d8))
- 价格设置 ([7209a70](https://github.com/pure-admin/vue-pure-admin/commit/7209a70a8680a78d6f5af4767a25aa4b3f6d0098))
- 价格设置编辑 ([5f47aad](https://github.com/pure-admin/vue-pure-admin/commit/5f47aada5046f588f3b86a4c4feda048021f2f21))
- 价格设置及基础内容 ([3613dda](https://github.com/pure-admin/vue-pure-admin/commit/3613dda8154c259596a1294ad785cb29d30ac38d))
- 价格设置接口 ([5495d4c](https://github.com/pure-admin/vue-pure-admin/commit/5495d4ca3d140f06e61d7f433b419b7b4c9d7707))
- 课程编辑基本信息 ([6c20a8d](https://github.com/pure-admin/vue-pure-admin/commit/6c20a8d36605174a06f3f2fb5edee19ce8f69f36))
- 课程管理首页添加草稿箱按钮 ([41cd8af](https://github.com/pure-admin/vue-pure-admin/commit/41cd8af75717788dbd8e7405a40d3a47a228023a))
- 课程详情草稿箱数量 ([aa58e7b](https://github.com/pure-admin/vue-pure-admin/commit/aa58e7b02e85d2fdb659d6db27067db6b8f630e5))
- 课期编辑内容回显 ([401ed31](https://github.com/pure-admin/vue-pure-admin/commit/401ed31ae124e9fb83f5ac85f1faa1b2a74d0009))
- 课期介绍和装备说明 ([c5ca1a1](https://github.com/pure-admin/vue-pure-admin/commit/c5ca1a1e40e2332f87478c0464493e0f7e1370bb))
- 手机号登陆 ([8f6726c](https://github.com/pure-admin/vue-pure-admin/commit/8f6726cea434ead25f3095ca5058ec8282d9dc79))
- 添加 vite-plugin-vue-inspector 插件以增强开发体验 ([2990daf](https://github.com/pure-admin/vue-pure-admin/commit/2990dafae34b51055f04a315dad51406d532a00c))
- 添加地图组件及相关功能，支持地址选择和定位 ([7448d2c](https://github.com/pure-admin/vue-pure-admin/commit/7448d2c0dd0914c9ea99877e062e7537c96cb035))
- 添加平台信息获取功能，更新网页标题和图标 ([687bdcf](https://github.com/pure-admin/vue-pure-admin/commit/687bdcf48d0235ae8e9e7ec0945e3f9b6ab8185e))
- 填写说明 ([5947f69](https://github.com/pure-admin/vue-pure-admin/commit/5947f69eed12e0c8d78d1bf8d655fc13e306f6ea))
- 新建基地领队讲师取消跳转功能 ([29288e1](https://github.com/pure-admin/vue-pure-admin/commit/29288e1d6e4a11eeacb150a5d5eecc7506bac0ef))
- 新增银行账户信息页面 ([525e9b7](https://github.com/pure-admin/vue-pure-admin/commit/525e9b74b2936b2db6560ebdd379e26fd0dfd4e7))
- 验证码登录部分 ([65b3efe](https://github.com/pure-admin/vue-pure-admin/commit/65b3efe0acec2a115cdd4707bb7e29f4a1bd695f))
- 用户协议接口及基础信息课程不可编辑 ([4bb370d](https://github.com/pure-admin/vue-pure-admin/commit/4bb370de6d506138d6cc635c7142c8d41c718524))
- 优化表格规格显示 ([d9e7950](https://github.com/pure-admin/vue-pure-admin/commit/d9e7950cd857146beb8a02e0b2fa156f63925ca0))
- 作业设计 ([936d623](https://github.com/pure-admin/vue-pure-admin/commit/936d6234ab1736d5e030d040a8e7ab0441cfa545))
- 作业设计及基础信息 ([95d0e9b](https://github.com/pure-admin/vue-pure-admin/commit/95d0e9b67021ac2abbda3a79b35d5b28ff2eceea))
- ai工具助手 ([8f88812](https://github.com/pure-admin/vue-pure-admin/commit/8f88812ca6674fdd71ce9506d7e7e62f9fb08e50))
- enhance build performance with esbuild optimizations ([82c695a](https://github.com/pure-admin/vue-pure-admin/commit/82c695a78d72295f13977f4a6e8d773772e16377))

### Bug Fixes

- 草稿箱步骤条3.0 ([675a5b8](https://github.com/pure-admin/vue-pure-admin/commit/675a5b8d9fb292f99ffad116b3ec59a6fb5bd1e5))
- 草稿箱步骤条4.0 ([39bcfbe](https://github.com/pure-admin/vue-pure-admin/commit/39bcfbeefc20301eb3777b333b6c3ad7e6a1448c))
- 编辑-行程安排 ([70fd54f](https://github.com/pure-admin/vue-pure-admin/commit/70fd54fc8cd1fe57675cc52ae360f049fddc7eb2))
- 编辑日志文案调整 ([237e22d](https://github.com/pure-admin/vue-pure-admin/commit/237e22db96cb82cb3e148e361acdcb67f35fb5d0))
- 编辑页课期名和图片调整 ([4fe3c4c](https://github.com/pure-admin/vue-pure-admin/commit/4fe3c4cb1a838ce55e6618334c7004ef519ab9d0))
- 表单内容上限增加 ([787f7eb](https://github.com/pure-admin/vue-pure-admin/commit/787f7ebf4e406ecf6ca7b6f232c6fb974f7436f1))
- 草稿箱-新增课期行程 ([fada76c](https://github.com/pure-admin/vue-pure-admin/commit/fada76c857dd1d6dc8694332b9f0bd36a31da364))
- 草稿箱编辑跳转 ([2b1002e](https://github.com/pure-admin/vue-pure-admin/commit/2b1002e56b9efe34d174372fd7a03c8d4f19a6b9))
- 草稿箱步骤条 ([f553a1e](https://github.com/pure-admin/vue-pure-admin/commit/f553a1e322366dcb558a528a0e4fb3be3f4c1cc5))
- 草稿箱步骤条+列表(完成度) ([4686f71](https://github.com/pure-admin/vue-pure-admin/commit/4686f71805cf1ee25a9675348d028baebd1516fb))
- 草稿箱步骤条2.0 ([a344506](https://github.com/pure-admin/vue-pure-admin/commit/a3445065b1a9fc7f29337e2038cdfe17a041ad75))
- 草稿箱步骤条5.0 ([2d5a7ce](https://github.com/pure-admin/vue-pure-admin/commit/2d5a7cecfa8669cab33bf9adb852747f226536f8))
- 草稿箱步骤条6.0 ([3003c01](https://github.com/pure-admin/vue-pure-admin/commit/3003c01b427670604b4289ba1f98648ee071fd59))
- 草稿箱导航栏bug ([8f61da2](https://github.com/pure-admin/vue-pure-admin/commit/8f61da2706b8b986885ab4530784b6818e78096d))
- 草稿箱分页接口 ([9c82da9](https://github.com/pure-admin/vue-pure-admin/commit/9c82da9438525f34613bb24592bc27e6036223d7))
- 草稿箱获取规格列表信息 ([ac3dfa9](https://github.com/pure-admin/vue-pure-admin/commit/ac3dfa9fab3ba9283da76686bde62efc240750e3))
- 草稿箱价格设置回显 ([d4e353b](https://github.com/pure-admin/vue-pure-admin/commit/d4e353b30bf483b470de60527122f824b00a4d68))
- 草稿箱课程标签和课期标签及编辑页面调整 ([7b9128f](https://github.com/pure-admin/vue-pure-admin/commit/7b9128f2f57d7e0c111f1c457a162142dc5347ec))
- 草稿箱列表 ([9b052dd](https://github.com/pure-admin/vue-pure-admin/commit/9b052ddcab8a2870371a41baf37eee950972d1ff))
- 草稿箱列表删除接口 ([274aa77](https://github.com/pure-admin/vue-pure-admin/commit/274aa77ddb7d1eaddedc7bf657be4a90e857ca9a))
- 草稿箱退出+图片占位 ([b7b7c2b](https://github.com/pure-admin/vue-pure-admin/commit/b7b7c2b62396d6246dc9b7aa83af18ffcf333b4a))
- 草稿箱退出功能调整 ([176a509](https://github.com/pure-admin/vue-pure-admin/commit/176a509ea511dc0e5df962be13603ee735eb85e7))
- 草稿箱新建-完成接口 ([55bf9d0](https://github.com/pure-admin/vue-pure-admin/commit/55bf9d0df3ff9c5928cb7e6f2263a55e1d3e49cb))
- 查询草稿数量接口+下一步 ([978a8a5](https://github.com/pure-admin/vue-pure-admin/commit/978a8a502b27db27ac93117fb53148d1a5bde415))
- 调整各管理页面的每页显示条数为15 ([c855528](https://github.com/pure-admin/vue-pure-admin/commit/c855528f0da08f0ef4445688e2fbcafa0f2c57ad))
- 复制课期 ([991571f](https://github.com/pure-admin/vue-pure-admin/commit/991571f63de46d1132564df71a45f001ec0b687d))
- 复制课期title图片丢失bug ([c024831](https://github.com/pure-admin/vue-pure-admin/commit/c024831e7ddaa27901bfe7431f7673809cfb97af))
- 更新百度地图API的AK ([7eb52cc](https://github.com/pure-admin/vue-pure-admin/commit/7eb52cc93bb122664b0ce9dc64768a0cfd02fa16))
- 更新课程管理路由及文件上传逻辑，增加文件大小限制提示 ([0c57a27](https://github.com/pure-admin/vue-pure-admin/commit/0c57a27e69d9872b1daa5dc98bb3fe3b63ef644f))
- 合并分支解决价格设置冲突 ([32acd49](https://github.com/pure-admin/vue-pure-admin/commit/32acd498683d2e9ad02d590e6406433a11ecbfa1))
- 基础信息 ([f1b85d5](https://github.com/pure-admin/vue-pure-admin/commit/f1b85d594856624b8b748aaf3e9eb613339ccde5))
- 基础信息编辑页面调整及图片限制 ([dc20dfc](https://github.com/pure-admin/vue-pure-admin/commit/dc20dfc1dfbcc269e2c867339c5db7df86d428b9))
- 基础信息调整及课程信息编辑调整及日期限制 ([1a0f7e8](https://github.com/pure-admin/vue-pure-admin/commit/1a0f7e8a9e96157eb97b1e4feb3c1467125154d3))
- 基础信息页面调整 ([73df37e](https://github.com/pure-admin/vue-pure-admin/commit/73df37e43155ab07d82ecee71e44bf169d39b673))
- 基础信息页字体加粗调整及新建基地等返回信息储存及页面跳转 ([606cd2e](https://github.com/pure-admin/vue-pure-admin/commit/606cd2e65b03ff575d51afbfdbf6463c327fb0f7))
- 加密userId ([10b3c7f](https://github.com/pure-admin/vue-pure-admin/commit/10b3c7f3621645c5ccc77a6ee17625e67bf225b2))
- 价格设置逻辑调整 ([4c7da55](https://github.com/pure-admin/vue-pure-admin/commit/4c7da55ae4a63de2c34a6697efdf65a3237f3278))
- 将小数点视为特殊符号的一种 ([5720e1d](https://github.com/pure-admin/vue-pure-admin/commit/5720e1dd8d4fa276445f6ffb40f9071d149ab96e))
- 课程编辑按钮显示 ([a0380dd](https://github.com/pure-admin/vue-pure-admin/commit/a0380dd185c6f8377f096ac983cfb7adb418708d))
- 课程详情展示课程标签 ([d7b5766](https://github.com/pure-admin/vue-pure-admin/commit/d7b5766bd1fe85154ea5fecd03bcccee592f13a0))
- 课期标签对齐 ([c777414](https://github.com/pure-admin/vue-pure-admin/commit/c777414c99a7867720ec6d897bc77d9663327c70))
- 课期标签及命名回显 ([a0a6798](https://github.com/pure-admin/vue-pure-admin/commit/a0a67985c67f697c9abd8f7d9122ff11f25bfe21))
- 课期草稿箱 ([9fd6f3b](https://github.com/pure-admin/vue-pure-admin/commit/9fd6f3b50926f49d15b65cff717cc0ffc566d2c6))
- 课期草稿箱新建-课期行程 ([c3ef8d7](https://github.com/pure-admin/vue-pure-admin/commit/c3ef8d7145e89b47f9c35a71a28d8db1b258fe3b))
- 课期草稿箱新建-完成2.0 ([1bbe63e](https://github.com/pure-admin/vue-pure-admin/commit/1bbe63ebaaca91883f5a29aa3c4076e2314cfc7c))
- 课期复制前后缀选项及命名示意调整 ([7932c9f](https://github.com/pure-admin/vue-pure-admin/commit/7932c9f25f0a7ecb1e6a995a2dcee9006c47ba5d))
- 课期基础信息+编辑的行程安排 ([3d3d3ad](https://github.com/pure-admin/vue-pure-admin/commit/3d3d3ad5d01a76094e8ddd53a13a359a24ef2ed8))
- 课期介绍等相类似页面bug修复 ([0c5cabe](https://github.com/pure-admin/vue-pure-admin/commit/0c5cabe67c961a7d3f2ea27c0927a8c23c50c92c))
- 课期介绍调整 ([2309f98](https://github.com/pure-admin/vue-pure-admin/commit/2309f98ca2ff9a83ebf8683dc4e8b019cab8fe1a))
- 课期介绍上一步bug ([a447e27](https://github.com/pure-admin/vue-pure-admin/commit/a447e27158c00a6ce23ff6162313b367f3f22b0c))
- 课期示意名没有展示问题 ([a0d0f9d](https://github.com/pure-admin/vue-pure-admin/commit/a0d0f9de405e77ff5bdf7d1a20ff0c01355b88ad))
- 课期详情-复制的课期名命设置 ([af6d46d](https://github.com/pure-admin/vue-pure-admin/commit/af6d46d4ba3a8f5d7386e7ab5f8fb90d530b0f50))
- 课期行程完成(部分) ([fdfb72d](https://github.com/pure-admin/vue-pure-admin/commit/fdfb72d5afdf2d6f2e5bcb2979724974e8db6dbc))
- 面包屑处理 ([bb4b928](https://github.com/pure-admin/vue-pure-admin/commit/bb4b92886ead56c46fff92e49740df5d4005d7e6))
- 年龄段限制+提示 ([77a21e5](https://github.com/pure-admin/vue-pure-admin/commit/77a21e5b6ae1c8b25af8f7b8a90e35d459c95c44))
- 前后缀给默认无及人数上限调整 ([04c6d23](https://github.com/pure-admin/vue-pure-admin/commit/04c6d234366b7fe7783c22860283b767cf1e39f9))
- 日志文案调整 ([4b224cb](https://github.com/pure-admin/vue-pure-admin/commit/4b224cbb143a18a5ecf974aa5abfb80ac8a19937))
- 上架开启团购开课时间过期文案调整 ([2af7965](https://github.com/pure-admin/vue-pure-admin/commit/2af79651400a0368914186bc6a11d333beaa7aa7))
- 手机登录参数 调整 ([04bd49d](https://github.com/pure-admin/vue-pure-admin/commit/04bd49d7c3c06a523dd89bfda4291e6b8e946ad1))
- 添加文件类型验证、大小限制 ([d25e2d6](https://github.com/pure-admin/vue-pure-admin/commit/d25e2d60b78036ccc80d2d7d3eaae37b24035f25))
- 统一优化各课程组件滚动条样式 ([e710146](https://github.com/pure-admin/vue-pure-admin/commit/e71014656c51429d6462fc0eaff15a5035ce0453))
- 图片张数限制及文案提示 ([3c55a3c](https://github.com/pure-admin/vue-pure-admin/commit/3c55a3c8ddc4a5f34714ccb1e03850895bc75ced))
- 退出文案调整及草稿删除 ([61fac3f](https://github.com/pure-admin/vue-pure-admin/commit/61fac3f4a64e8644be2fd8bc4d59830a7c73a315))
- 完成bug+选中step基础按钮 ([eee100c](https://github.com/pure-admin/vue-pure-admin/commit/eee100c431be2c866416529e05a714c743bcafc0))
- 无前后缀上传和编辑时标签及年龄段回显 ([4e4c2eb](https://github.com/pure-admin/vue-pure-admin/commit/4e4c2eb50863ffbeab37ca7788f21032d0603cd1))
- 下一步按钮 ([1317836](https://github.com/pure-admin/vue-pure-admin/commit/13178364438c621afca8afa92a91da960a50b019))
- 下一步完成标识 ([7746126](https://github.com/pure-admin/vue-pure-admin/commit/7746126935b0cef4f40f2f56589ab389ef394c94))
- 新建-完成 ([1e4344a](https://github.com/pure-admin/vue-pure-admin/commit/1e4344a3e99a85064966f000d17ce92b65c39a89))
- 新建草稿箱bug ([11bd501](https://github.com/pure-admin/vue-pure-admin/commit/11bd5017d0f3e5bb4b38fe394946952926e6ccf0))
- 新建完成+草稿箱列表删除提示(bug) ([eac1dac](https://github.com/pure-admin/vue-pure-admin/commit/eac1dac0a8a6028f28e89b91bbc1aa7dde259e87))
- 新建完成按钮接口接入 ([d72ebf4](https://github.com/pure-admin/vue-pure-admin/commit/d72ebf4e21be92db28bcbffa84e26c8637c94d22))
- 新建完成标签回显+课期详情复制接口完成 ([e1b7b15](https://github.com/pure-admin/vue-pure-admin/commit/e1b7b1501974adf42bf68bcb477477b480f273f2))
- 新增课程新增基地后图片回显有问题 ([3cc263c](https://github.com/pure-admin/vue-pure-admin/commit/3cc263c5c67506543b9d1d2eb677dece66fcaa31))
- 行程点+复制课期前后缀bug ([4ca3220](https://github.com/pure-admin/vue-pure-admin/commit/4ca3220fca2bbfacd4dfe3eccadd6543c29cd802))
- 修复(bug) ([63748b1](https://github.com/pure-admin/vue-pure-admin/commit/63748b12ed99f5a85046ffdb780edcc678dcd0ab))
- 修复bug ([0165c73](https://github.com/pure-admin/vue-pure-admin/commit/0165c7384ab739d791f4d21fdbf374f24cbf98ba))
- 修复bug ([6b60a2e](https://github.com/pure-admin/vue-pure-admin/commit/6b60a2ea3b116332b786a2f8f5322ff438df5d62))
- 修改上课跟踪标签名称为课堂跟踪 ([290eb4d](https://github.com/pure-admin/vue-pure-admin/commit/290eb4dfdebc224837472a9d6ff4878dd08692e4))
- 修改bug ([d690ace](https://github.com/pure-admin/vue-pure-admin/commit/d690ace2a9b44ac5fb0b3c88765469a19505687c))
- 修正上课跟踪标标签称及订单详情展示 ([da28a1c](https://github.com/pure-admin/vue-pure-admin/commit/da28a1c81b030256a98ccef85cf634fa75dde2b8))
- 选中step中的基础信息里的校验 ([ba789e9](https://github.com/pure-admin/vue-pure-admin/commit/ba789e9b7abf627dc1db75b227c8cddf70455b4d))
- 银行信息修复面包屑调整样式 ([adca8a5](https://github.com/pure-admin/vue-pure-admin/commit/adca8a5febed6b403aac0b3d5f7bd11d96e98519))
- 隐藏课程介绍作业设计等的编辑按钮 ([18b8bd4](https://github.com/pure-admin/vue-pure-admin/commit/18b8bd4b21a0229242c6194111a2be3d14ed23f4))
- 优化订单详情路由逻辑，移除不必要的注释代码 ([9f7a0fa](https://github.com/pure-admin/vue-pure-admin/commit/9f7a0fa5be570ee842c876f285da078ad631c7ff))
- 优化账号编辑功能 ([1123682](https://github.com/pure-admin/vue-pure-admin/commit/1123682de1f92f6d00700aaae8806a4d62163cc5))
- 优化账号创建表单手机号和身份证号的加密解密逻辑，确保在不同状态下正确处理数据 ([bbe53e4](https://github.com/pure-admin/vue-pure-admin/commit/bbe53e411b6c71a965e228dc71208c2c4bffad79))
- 优化账号创建和编辑功能，调整手机号和身份证号的加密解密逻辑 ([ae01ae0](https://github.com/pure-admin/vue-pure-admin/commit/ae01ae08f1d9b4ffa4b643396203f394a77666f8))
- 智能填充行程内容的 确认+取消 ([44ae1a8](https://github.com/pure-admin/vue-pure-admin/commit/44ae1a848065cfbdbae97271552504e31610e98d))
- api地址修改 ([2ffaa86](https://github.com/pure-admin/vue-pure-admin/commit/2ffaa86039a26d2766581b566059e1ac8d0dc832))
- element-plus升为2.9.11、alert填写说明调整 ([eabc1de](https://github.com/pure-admin/vue-pure-admin/commit/eabc1deaf0c230c450aa2907673702d90a08c936))

## [1.4.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.3.0...v1.4.0) (2025-06-04)

### Features

- 富文本编辑器增强，添加自定义粘贴处理器 ([03f3f8c](https://github.com/pure-admin/vue-pure-admin/commit/03f3f8c19786adb197beec022b32f86d3938cdbe))
- 课期创建页面优化，使用nanoid生成唯一值替代原索引逻辑 ([b679c24](https://github.com/pure-admin/vue-pure-admin/commit/b679c240c133765065e4b85c60bb727f01616ffd))
- 课期创建页面优化，支持日期选择交互及动态更新星期显示 ([82f3553](https://github.com/pure-admin/vue-pure-admin/commit/82f35534db711f1bce98db26a9107e30f4991111))
- 添加文件Excel、Word、PDF和PPTX格式预览组件 ([4ed2c2e](https://github.com/pure-admin/vue-pure-admin/commit/4ed2c2e37d61583d1f4849142fd3e18e21385b4f))

### Bug Fixes

- 日志管理全选 ([101d11e](https://github.com/pure-admin/vue-pure-admin/commit/101d11e89805aab9ec0955cb0b478cc5d26c768c))
- 修正文件类型匹配，更新Word和PPTX为更通用的文档类型 ([0db7caa](https://github.com/pure-admin/vue-pure-admin/commit/0db7caa5388d43e81c59b2feed4f41a8de616b9c))
- 增加Office文档预览支持 ([39b772e](https://github.com/pure-admin/vue-pure-admin/commit/39b772ec109540e84fd092373400e8cc31411297))
- 注释掉token刷新逻辑 ([2655748](https://github.com/pure-admin/vue-pure-admin/commit/2655748cab55befe9896f80a96b888cae4bac5f8))

## [1.3.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.2.0...v1.3.0) (2025-05-27)

### Features

- 集成图片缩略图代理服务 ([8b7691d](https://github.com/pure-admin/vue-pure-admin/commit/8b7691d525394da6a950106161f6882b23859a9e))
- 课程当期详情伸缩功能表单适配 ([d80ba43](https://github.com/pure-admin/vue-pure-admin/commit/d80ba4345ec88e16ba4618f695b6a06194a7921e))
- 课期批量删除功能优化，增加行键配置和保留选择状态 ([a327935](https://github.com/pure-admin/vue-pure-admin/commit/a32793595d036e6168c25249c078a696f8b1c897))
- 课期批量删除接口增加操作日志参数及作业批改界面优化 ([cdc158c](https://github.com/pure-admin/vue-pure-admin/commit/cdc158c2920ec1dd0f37ee215e6c7f67cd6fe8f8))
- 新增课期批量删除功能及选择限制逻辑 ([787edc3](https://github.com/pure-admin/vue-pure-admin/commit/787edc30a69646f5f4c232d4416b244c25de6eb3))

### Bug Fixes

- 编辑课程缓存 ([3b6936c](https://github.com/pure-admin/vue-pure-admin/commit/3b6936cd6bfa5db3056212b90391bb10ed6ff35c))
- 查看课程评价评价数量不对 ([12936b4](https://github.com/pure-admin/vue-pure-admin/commit/12936b44ea13a244718c63e08327eca2c9986ac8))
- 当期详情出现重复多张预览图片问题 ([0554074](https://github.com/pure-admin/vue-pure-admin/commit/0554074d1e25da0c3e5a704b25cfc629c1c68133))
- 当期详情阴影调整 ([431399c](https://github.com/pure-admin/vue-pure-admin/commit/431399cdb348d46a59f9967a993cba7aa452f9ff))
- 二次确认弹框ui调整 ([0d3f31e](https://github.com/pure-admin/vue-pure-admin/commit/0d3f31e860fb286476119e6a938e8448afc867c6))
- 课程当期详情收缩按钮样式调整 ([7f4be3c](https://github.com/pure-admin/vue-pure-admin/commit/7f4be3c727930be5fc7aa92460a14ac89a72fbb0))
- 课程当期详情页面缓存配置及工作详情组件样式注释调整 ([91ebd17](https://github.com/pure-admin/vue-pure-admin/commit/91ebd179f8a2abc5a4b38e3e41d8e983c36420ce))
- 课程管理列表页高度自适应 ([1f3e947](https://github.com/pure-admin/vue-pure-admin/commit/1f3e947ebafec14c0bf74a51bfc6478de909dfc5))
- 课程详情描述表格宽度调整 ([39d95a5](https://github.com/pure-admin/vue-pure-admin/commit/39d95a53523c579800a58bbb107d846b86ae2fad))
- 课期创建缓存 ([6423a5f](https://github.com/pure-admin/vue-pure-admin/commit/6423a5f717673cbdf9c26d89500042618d47b7db))
- 课期当期详情伸缩按钮样式调整 ([ee5a41a](https://github.com/pure-admin/vue-pure-admin/commit/ee5a41a662b7376d1bbe0734f1404a7437dfdebb))
- 课期详情批量删除优化 ([8913d8b](https://github.com/pure-admin/vue-pure-admin/commit/8913d8b654aa242d9c173c214c961a3622ac499d))
- 领队讲师跳转课期详情面包屑调整及课期详情相关编辑等按钮隐藏 ([661a646](https://github.com/pure-admin/vue-pure-admin/commit/661a64618af7f51cbdae6af28cd4b9c0296cb9de))
- 批量删除优化 ([b31820c](https://github.com/pure-admin/vue-pure-admin/commit/b31820ce4f4056d84f519781be3ee46aee40a427))
- 批量删除优化2.0 ([074cbf4](https://github.com/pure-admin/vue-pure-admin/commit/074cbf43c5f8bedf0688adff71f0414b9cadaf91))
- 评价列表展示所有评价 ([1fdeaf5](https://github.com/pure-admin/vue-pure-admin/commit/1fdeaf5a6a29226327f071cd3635d1c792e07229))
- 评价筛选输入空格优化 ([b621fc9](https://github.com/pure-admin/vue-pure-admin/commit/b621fc93a39e1d8de06023308089d3db6e9c421e))
- 全部评价编译失败问题 ([a756165](https://github.com/pure-admin/vue-pure-admin/commit/a756165f06b9648177191a0c0aa592e08252de1f))
- 全部评价中课程状态ui优化 ([b7cc3d5](https://github.com/pure-admin/vue-pure-admin/commit/b7cc3d5798cea7cf7a9cba848b4cd71057a7c663))
- 使用v-preview自定义预览指令点击遮罩层关闭图片预览 ([5f0987e](https://github.com/pure-admin/vue-pure-admin/commit/5f0987e1ce281738a84324d6d0a4e803ec02243c))
- 退单详情状态更新 ([5c7bfe1](https://github.com/pure-admin/vue-pure-admin/commit/5c7bfe1bacdbc3f941d4ad5d3d1e50793ff3f512))
- 优化课程批量删除失败提示及UI格式 ([057453a](https://github.com/pure-admin/vue-pure-admin/commit/057453afee67195fe596b8b73de0f7a747afba00))
- 作业设计图片无法展示问题 ([99f72bc](https://github.com/pure-admin/vue-pure-admin/commit/99f72bc47fdd37d6ec1af629873c60e17c103362))

## [1.2.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.1.0...v1.2.0) (2025-05-19)

### Features

- 课程当期详情新增伸缩展示 ([36f8737](https://github.com/pure-admin/vue-pure-admin/commit/36f8737c3086e9b5e7254bc50403335114430e67))
- 全部评价功能 ([87985c9](https://github.com/pure-admin/vue-pure-admin/commit/87985c9d147329c9df33f15e2b5173284226f0e0))
- 添加图片代理功能及缩略处理 ([ce393f1](https://github.com/pure-admin/vue-pure-admin/commit/ce393f139c279629ac67d96dd445d221d2ba9738))

### Bug Fixes

- 调整课程表格列表组件样式注释与背景色 ([b68b38b](https://github.com/pure-admin/vue-pure-admin/commit/b68b38b2a07b7457ca42013ab8e2baeb121bea33))
- 日志表单排版 ([7459168](https://github.com/pure-admin/vue-pure-admin/commit/7459168b6e703cbf4905350c5200e3a282651188))
- 图片代理正式服地址 ([5f56840](https://github.com/pure-admin/vue-pure-admin/commit/5f568408077fc7d345595105abaec64b47ec5931))
- 新建领队/讲师缓存 ([ce1b404](https://github.com/pure-admin/vue-pure-admin/commit/ce1b40464f775beb0b1b6a8461db122f54d4dcea))

## [1.1.0](https://github.com/pure-admin/vue-pure-admin/compare/v1.0.0...v1.1.0) (2025-05-16)

### Features

- 新增讲师领队冻结相关展示及功能调整 ([53a3c2d](https://github.com/pure-admin/vue-pure-admin/commit/53a3c2d13c34feff1f690b06aac4d97593025fd0))
- 新增新建领队、讲师 ([7796b03](https://github.com/pure-admin/vue-pure-admin/commit/7796b03826f39242e8d15254f5b4c39f3fd29125))

### Bug Fixes

- 富文本拼接 ([1ea62eb](https://github.com/pure-admin/vue-pure-admin/commit/1ea62ebd8a3348426f32119a0e97a686ddb9e942))
- 机构管理日志埋点 ([f934f0e](https://github.com/pure-admin/vue-pure-admin/commit/f934f0ec05241c0a6c5eb134fa75c6b13f3867cc))
- 开启团购和上架因必填项未填提示及行程安排时间点异常提示 ([fc44295](https://github.com/pure-admin/vue-pure-admin/commit/fc442957d365ad1fe92f5548abd5b0b798faa925))
- 课程类型选择器在创建与编辑页设为禁用状态 ([c5b02e0](https://github.com/pure-admin/vue-pure-admin/commit/c5b02e0d8134a72b1cfd101cc81fd84f4f6c9a62))
- 课期详情数据更新 ([bdb5586](https://github.com/pure-admin/vue-pure-admin/commit/bdb5586465fcdab85dfc0bcb051b69c24367f3c7))
- 屏蔽批量删除 ([8efb007](https://github.com/pure-admin/vue-pure-admin/commit/8efb007e133c3f8b1add29a9695399b942d93265))

## [1.0.0](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.8...v1.0.0) (2025-05-15)

### Bug Fixes

- 领队、讲师、基地面包屑 ([a4a420e](https://github.com/pure-admin/vue-pure-admin/commit/a4a420eadd67c9176a6184b393348f452854f496))
- 人数限制调整 ([a69c172](https://github.com/pure-admin/vue-pure-admin/commit/a69c17294d2b0ef9358f087f7301abec6ae689d2))
- 图片点击遮罩层关闭，课程档期详情路由缓存 ([cd7a0e2](https://github.com/pure-admin/vue-pure-admin/commit/cd7a0e2aba6749b102baba0cc36968d47c393bc2))
- 新建账号验证码调整 ([8f3cdbd](https://github.com/pure-admin/vue-pure-admin/commit/8f3cdbd106c870e804e5794752a2a3f28a96828a))
- 行程点可以等于开课时间 ([86f71de](https://github.com/pure-admin/vue-pure-admin/commit/86f71de79e87c1ae31f91ea5e1019f26cfcd8edd))
- 修复新增领队/讲师控制台报错 ([3feae53](https://github.com/pure-admin/vue-pure-admin/commit/3feae5363f43847d36f86f839eb36636b52399a4))
- 用户评价删除筛选项、期号等，调整机构回复展示 ([e90331a](https://github.com/pure-admin/vue-pure-admin/commit/e90331a9d933343a72eb4807a21c87b7269c01ee))
- 用户评论bug ([8f446bb](https://github.com/pure-admin/vue-pure-admin/commit/8f446bbaac89940977003bdb646467fd8038c5ee))
- 作业详情bug修复 ([0aff29f](https://github.com/pure-admin/vue-pure-admin/commit/0aff29f00f9c8c674b57a0e760fec6accb884da2))

### [0.0.8](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.7...v0.0.8) (2025-05-13)

### Features

- 讲师领队新增冻结相关展示 ([ad4c225](https://github.com/pure-admin/vue-pure-admin/commit/ad4c225733fd21e193158a8459b6fd97fef1633d))
- 领队详情新增冻结按钮和状态展示 ([d5a9f72](https://github.com/pure-admin/vue-pure-admin/commit/d5a9f7216bb64804436b8a9dcdf6b11bd072056d))
- 学生情况添加报名人数和上限人数、课程管理添加冻结状态筛选项 ([42cfe65](https://github.com/pure-admin/vue-pure-admin/commit/42cfe6568ec9e98c39d79ff033e9b3b3b4f3660e))
- 账号管理新增冻结相关展示 ([45b8682](https://github.com/pure-admin/vue-pure-admin/commit/45b86825afd8d255665ae9a01b8bc404426385ca))

### Bug Fixes

- 课期详情内容展示课程状态平台下架原因及审核状态驳回审核原因 ([a031a84](https://github.com/pure-admin/vue-pure-admin/commit/a031a84b544f61e0997c96296a6521c8546d5d2f))
- 用户密码新增输入上限 ([fbaae20](https://github.com/pure-admin/vue-pure-admin/commit/fbaae2085d74a4667e35caa9e58c9f94ef68c811))
- 用户评价文字提示过长调整 ([a6321c3](https://github.com/pure-admin/vue-pure-admin/commit/a6321c3bcdeb9e4477985fc7c76631177d65bccd))

### [0.0.7](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.6...v0.0.7) (2025-05-09)

### Features

- 登录新增日志 ([6f1d1cc](https://github.com/pure-admin/vue-pure-admin/commit/6f1d1ccf274260ec5e665ba80a104831ae1ed9cb))
- 角色管理按钮级权限 ([91c01a3](https://github.com/pure-admin/vue-pure-admin/commit/91c01a3a952a536effe989a65162c8a6b72099e4))
- 课期编辑新增基地、新增领队和讲师 ([bac0619](https://github.com/pure-admin/vue-pure-admin/commit/bac061986455a18d2c095bb27f042537d5fc20e1))
- 删除课程 ([5cedff0](https://github.com/pure-admin/vue-pure-admin/commit/5cedff0126073797e01cf844ae03be29ecaff51e))
- 添加面包屑 ([8648f13](https://github.com/pure-admin/vue-pure-admin/commit/8648f13912de230cb7937a1034d54068696337bd))

### Bug Fixes

- 按钮防抖 ([4d5924b](https://github.com/pure-admin/vue-pure-admin/commit/4d5924bb2f346ce4eb6d8c22e52e730def2c3b73))
- 编辑课程按钮显示 ([1b367dc](https://github.com/pure-admin/vue-pure-admin/commit/1b367dc8def00a8316488f764baf5bdcda2a0e24))
- 冲突 ([c9f6e1b](https://github.com/pure-admin/vue-pure-admin/commit/c9f6e1b3c196b66a8a06059b7d76773b74e007fa))
- 创建课期后路由跳转 ([9ece4fa](https://github.com/pure-admin/vue-pure-admin/commit/9ece4fae985cc1d4e4cd69928db7368a9e3dbf7d))
- 创建课期日志文案完善 ([1922bf2](https://github.com/pure-admin/vue-pure-admin/commit/1922bf260b49cc043545e200a6aeba0013497e5d))
- 当期详情编辑按钮被遮挡 ([5f2d7a5](https://github.com/pure-admin/vue-pure-admin/commit/5f2d7a5bb044c51620dd9a19f4d4b0e85a62a273))
- 当期详情表格调整及课期编辑领队讲师冻结账号后显示问题 ([b13dda3](https://github.com/pure-admin/vue-pure-admin/commit/b13dda3efb4c3b779ffa304376dc69c372c60e96))
- 当前课期列表展示 ([3c84c92](https://github.com/pure-admin/vue-pure-admin/commit/3c84c92eae4ff353b5a1cf20acb580f64a0559a2))
- 当前课期团购分享按钮渲染异常 ([f687883](https://github.com/pure-admin/vue-pure-admin/commit/f687883e5daeed6e1f6dda3272cb15e2d48745ee))
- 登录密码验证 ([0b2585d](https://github.com/pure-admin/vue-pure-admin/commit/0b2585d9fd712e138b1cb439e678abc238f9fde2))
- 登录异常文案 ([8c57ce3](https://github.com/pure-admin/vue-pure-admin/commit/8c57ce303d91a7ac2ca0d0a4764db1af191cd9d2))
- 调整二次弹窗文案 ([ea89fb8](https://github.com/pure-admin/vue-pure-admin/commit/ea89fb89d1ccf941c7c66412d90de1155464cb92))
- 调整手机验证 ([67afb2a](https://github.com/pure-admin/vue-pure-admin/commit/67afb2ae56b7ffc32bacdeb470ed268459066e36))
- 调整眼睛样式 ([c63d89b](https://github.com/pure-admin/vue-pure-admin/commit/c63d89bed1db7269baaed9cbf9e6f5b44f90101a))
- 订单管理调整 ([da76d15](https://github.com/pure-admin/vue-pure-admin/commit/da76d15d5b7da6ab3bb665ce79d337e7672e0e7e))
- 订单详情按钮展示修改，订单列表筛选、展示修改 ([9064fcc](https://github.com/pure-admin/vue-pure-admin/commit/9064fcc9cac4c019908fb36be74a073b4f018d9a))
- 订单详情确认退单，驳回功能调整 ([89e53c0](https://github.com/pure-admin/vue-pure-admin/commit/89e53c07a74d1b7a1c4e4119e062101bfff44936))
- 二维码调整 ([18bc455](https://github.com/pure-admin/vue-pure-admin/commit/18bc4557e000bee692b204bb02e121305853cc3e))
- 机构端隐藏权限管理 ([4483da1](https://github.com/pure-admin/vue-pure-admin/commit/4483da18dca4b154cb219e0acf0857358985c486))
- 基地管理按钮颜色调整 ([cb1dda0](https://github.com/pure-admin/vue-pure-admin/commit/cb1dda045f41af052fc28122ed81b4ee41b2035a))
- 基地管理添加 onActivated ([628181a](https://github.com/pure-admin/vue-pure-admin/commit/628181a3a1dab2257e5e6ec1014d444c64c67fed))
- 价格展示加上单位 ([d60b217](https://github.com/pure-admin/vue-pure-admin/commit/d60b217d05f7ad275ca9041eeeaf9216ef31a3b8))
- 教师管理面包屑、sm4空值检测 ([8a6a825](https://github.com/pure-admin/vue-pure-admin/commit/8a6a82580abcd4488f0f393385b912d95c778f0e))
- 解决订单详情报错 ([cb669b5](https://github.com/pure-admin/vue-pure-admin/commit/cb669b582530e5b338ff60a470daee637c92c6b9))
- 解决课程创建失败 ([8e0c57f](https://github.com/pure-admin/vue-pure-admin/commit/8e0c57f3542f107e99e8e498f4bce2bba6bcdff0))
- 课程报告编辑按钮显示 ([c31dc86](https://github.com/pure-admin/vue-pure-admin/commit/c31dc862ccf5cf8629c065d11a4998750672cfae))
- 课程创建加loading、课期创建图片未上传、新增基地领队讲师 ([6eef272](https://github.com/pure-admin/vue-pure-admin/commit/6eef2721e1a9c5107f7cd902d3525c186850c6f3))
- 课程创建未选择课程类型报错文案调整 ([904522c](https://github.com/pure-admin/vue-pure-admin/commit/904522c0fbbaf52bbba2cd1c49dc715c81108e73))
- 课程创建页面调整 ([a3d424b](https://github.com/pure-admin/vue-pure-admin/commit/a3d424b38a05e8f400a6e7015bcf830ff5319b6e))
- 课程冻结状态展示及课期课程编辑按钮展示 ([0a07a68](https://github.com/pure-admin/vue-pure-admin/commit/0a07a6831fb1fcd96853fac312c43160a7eaeb19))
- 课程管理列表页调整 ([463bbb1](https://github.com/pure-admin/vue-pure-admin/commit/463bbb1aa8a3d59ae6156f58ea90bbbf3ae0bebb))
- 课程管理模块时间选择调整 ([d1880c3](https://github.com/pure-admin/vue-pure-admin/commit/d1880c34ece39b5febdfda6f229c6e35578fb0c4))
- 课程管理添加keep-alive ([e4c5d3e](https://github.com/pure-admin/vue-pure-admin/commit/e4c5d3e82d3ca97490e16cb620735b474034fa49))
- 课程管理相关埋点 ([296e7ee](https://github.com/pure-admin/vue-pure-admin/commit/296e7ee84ba7ce58addcd282374af750a29acb0c))
- 课程管理学生情况中家长电话优化 ([3937fea](https://github.com/pure-admin/vue-pure-admin/commit/3937feaa7b3015100ecebfd2dbb9b91caf480098))
- 课程管理用户评价评分搜索优化 ([828d5eb](https://github.com/pure-admin/vue-pure-admin/commit/828d5eb23892a3a7c5515b801a8709b80dcde507))
- 课程管理用户评价评价内容及机构回复显示优化 ([4503eec](https://github.com/pure-admin/vue-pure-admin/commit/4503eece8bf500782fd6d8fd523a67cb4774f3bb))
- 课程介绍等加loading状态 ([1996d2f](https://github.com/pure-admin/vue-pure-admin/commit/1996d2fb7eebc3424d4a88d9a291ab48d52e5494))
- 课程课期创建基地领队讲师无数据提示及跳转对应页面添加 ([bef50b5](https://github.com/pure-admin/vue-pure-admin/commit/bef50b507daeea322f4cfcecbbf2164ffc3c7242))
- 课程审核状态渲染及课程审核筛选 ([82cb8f7](https://github.com/pure-admin/vue-pure-admin/commit/82cb8f779a7f1d568024a21da3a68a3ee68a9297))
- 课程详情调整 ([d9011e0](https://github.com/pure-admin/vue-pure-admin/commit/d9011e06906e8c8c2df61b10e9d7cafeaee073ab))
- 课程详细资料和课程课期表格不展示基地 ([d04defe](https://github.com/pure-admin/vue-pure-admin/commit/d04defe9f587b67614b354724b9e748a4273797c))
- 课期编辑不能早于当前时间限制 ([a0582d4](https://github.com/pure-admin/vue-pure-admin/commit/a0582d430ddf0dbf739b7bcc26e085d9a876d14b))
- 课期创建关闭报名时间小时填写优化 ([2de3d82](https://github.com/pure-admin/vue-pure-admin/commit/2de3d8228d9efd2ed57a9e01808356866b42be89))
- 课期创建新建基地/新建领队/新建讲师新建后默认选中 ([c3a5840](https://github.com/pure-admin/vue-pure-admin/commit/c3a5840fac01d587b7322728f06fe8091ac15754))
- 课期列表接口调整 ([b94d654](https://github.com/pure-admin/vue-pure-admin/commit/b94d6545446c924d211626e1b6035eaf6f032420))
- 课期内容表格展示调整 ([abf7b76](https://github.com/pure-admin/vue-pure-admin/commit/abf7b761ea0957f8981bae00d198bb9b2a9497aa))
- 课期取消审核及课程状态和审核状态调整 ([8d4b298](https://github.com/pure-admin/vue-pure-admin/commit/8d4b298acfc8007ac3b47a641c304d800cfdc42e))
- 领队、教师脱敏 ([4c49839](https://github.com/pure-admin/vue-pure-admin/commit/4c49839564b2ecdf1ccd3f4b7a823be288b95b2d))
- 领队、教师详情缓存 ([1091a8f](https://github.com/pure-admin/vue-pure-admin/commit/1091a8fc43a8fded0ae5b29fc195a89422e6863e))
- 路由配置调整 ([aeb48e2](https://github.com/pure-admin/vue-pure-admin/commit/aeb48e2ef78dac5e41ff9e74f2ecdd050299ee12))
- 路由权限过滤异常 ([5a5fa9b](https://github.com/pure-admin/vue-pure-admin/commit/5a5fa9bb792bb3cec7932c5c7ed08aedf6d87de1))
- 面包屑及页面调整 ([e5d258d](https://github.com/pure-admin/vue-pure-admin/commit/e5d258d8a848e9b280dbcfcd15717644faa628b2))
- 全部退单展示条件修改 ([1563bd3](https://github.com/pure-admin/vue-pure-admin/commit/1563bd35be33f11dc14bf238c8fcc58af47e8189))
- 全新创建弹框显示问题 ([741f743](https://github.com/pure-admin/vue-pure-admin/commit/741f7434ad76bdf9a74d829f228ff8ae7a60f827))
- 全新复制携带课程信息问题 ([921a3ee](https://github.com/pure-admin/vue-pure-admin/commit/921a3ee0a1bcb4f621ad09970fceac4dcd458882))
- 日期搜索修改 ([8ba2f7c](https://github.com/pure-admin/vue-pure-admin/commit/8ba2f7cd7135c5da5c5a0d1571e2748e36428944))
- 日志埋点 ([419ce1c](https://github.com/pure-admin/vue-pure-admin/commit/419ce1c8f78c1d3120662ddcf9314000b3c361f6))
- 日志埋点，角色管理文案待修改 ([b307d66](https://github.com/pure-admin/vue-pure-admin/commit/b307d66b336b0559a08a8f9fab4092e7e532d74b))
- 日志文案调整 ([c991b0a](https://github.com/pure-admin/vue-pure-admin/commit/c991b0a848cfc604f25cc5c9188a7f4d0e8b65db))
- 删除课期及课程接口调整 ([e2d4e34](https://github.com/pure-admin/vue-pure-admin/commit/e2d4e349e0e08dec3fb1f1483b2ec85b2cb0e425))
- 上下架失败提示及课期创建课期名调整 ([a1fa34f](https://github.com/pure-admin/vue-pure-admin/commit/a1fa34f42f16c2662ab1054c50fab793e1add399))
- 手机号调整 ([c0d7ceb](https://github.com/pure-admin/vue-pure-admin/commit/c0d7cebb317d3336d8472ee1bb4bfb63718bed10))
- 提示课期创建失败原因 ([9a6500d](https://github.com/pure-admin/vue-pure-admin/commit/9a6500d03d66a25dc3afe44bd90fd5c89062d937))
- 替换默认头像 ([ef34dec](https://github.com/pure-admin/vue-pure-admin/commit/ef34dec6f0ee8e3e16b5001340f877a236046414))
- 统一操作按钮样式 ([2e2f01f](https://github.com/pure-admin/vue-pure-admin/commit/2e2f01f659f8b80c32354f824bee2416e4e93cde))
- 团购分享页调整及关闭团购跳转 ([362dc9e](https://github.com/pure-admin/vue-pure-admin/commit/362dc9e054a5fd42132f3bd807f95cec01a10215))
- 团购分享页复制链接调整 ([075dc54](https://github.com/pure-admin/vue-pure-admin/commit/075dc546fe39338d4b5d6470600078ba1bef2250))
- 团购分享页ui调整 ([d23b120](https://github.com/pure-admin/vue-pure-admin/commit/d23b12090cebd9ef9684e59e8230a7d862f66f62))
- 退单驳回按钮条件修改 ([b840d67](https://github.com/pure-admin/vue-pure-admin/commit/b840d671dc172acf4c39a8353af6dcfa46a36044))
- 文件上传限制调整 ([4c7c9fc](https://github.com/pure-admin/vue-pure-admin/commit/4c7c9fca9845dc2ba6799da228a2beeebc7bda07))
- 新建行程点时间调整 ([2e6f9f9](https://github.com/pure-admin/vue-pure-admin/commit/2e6f9f9d779410e1397a0e9ff6da85f21790fdb7))
- 新增密码锁定提示、领队/讲师修改手机号调整 ([8a6d649](https://github.com/pure-admin/vue-pure-admin/commit/8a6d6492ba9f65a15d3692661f9aab7e4d89cdb6))
- 行程点课期编辑时间限制及提示及loading状态 ([2fcb433](https://github.com/pure-admin/vue-pure-admin/commit/2fcb433699fdc10aa900d6e074e23c7a3f3a553b))
- 修复富文本图片展示拼接空格 ([fed7472](https://github.com/pure-admin/vue-pure-admin/commit/fed747264cb5f218cb5f89c36604270380c3b9a9))
- 修复新增课程的时间选择限制 ([a2b7c89](https://github.com/pure-admin/vue-pure-admin/commit/a2b7c89739d593ef6e152ba419f7a61ce9b285c5))
- 修复页面缓存 ([c1bf5d7](https://github.com/pure-admin/vue-pure-admin/commit/c1bf5d7bd333774a482e7ea05bd25d2a984d118f))
- 修改订单支持多文件上传 ([058daee](https://github.com/pure-admin/vue-pure-admin/commit/058daee85bbb56797ebecf6363302ff418cf7138))
- 修改了日志只在成功后请求 ([7bb1840](https://github.com/pure-admin/vue-pure-admin/commit/7bb1840b027db22dd63f7e5c5a2db252337b4ef5))
- 修改日志文案 ([dfcba56](https://github.com/pure-admin/vue-pure-admin/commit/dfcba56a162a47c8bc8d975d11161ffd47477fdc))
- 修改手机号调整 ([33c532e](https://github.com/pure-admin/vue-pure-admin/commit/33c532ea19d64fd377812e23f8554f9ddc053a64))
- 修改token冻结文案 ([50f70e5](https://github.com/pure-admin/vue-pure-admin/commit/50f70e5d020182f55b7921ef7c8de74832ae2f8b))
- 页面刷新路由参数丢失修复 ([49172cd](https://github.com/pure-admin/vue-pure-admin/commit/49172cdaa70905598a243a2e99ac8c3882a7e09c))
- 优化当期详情按钮渲染及课程详情数据更新 ([5b31e32](https://github.com/pure-admin/vue-pure-admin/commit/5b31e321165053e5f2ff1b1ef9da029a28575c61))
- 账号、日志、订单、财务、领队、讲师表格缩放动态高度 ([bfa67d9](https://github.com/pure-admin/vue-pure-admin/commit/bfa67d9b9a433ed508c688f15bcf2c3a9dcad6c8))
- 作业批改后提交按钮未显示 ([cb5ebb6](https://github.com/pure-admin/vue-pure-admin/commit/cb5ebb681e342ef3b6c60701046b481a6666f977))
- 作业批改限制为数字输入，新增行程限制选择日期 ([49304f5](https://github.com/pure-admin/vue-pure-admin/commit/49304f57ab17e7f4032e74db38b25f2e2c57603e))

### [0.0.6](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.5...v0.0.6) (2025-04-17)

### Bug Fixes

- 轮播图替换走马灯 ([e2ffc12](https://github.com/pure-admin/vue-pure-admin/commit/e2ffc12ca49aac8f22c5bdcf3bcf57a58c81b425))
- 去掉输入框首尾空格 ([b50fef7](https://github.com/pure-admin/vue-pure-admin/commit/b50fef7f24485cb436f5b6305d826f93691d2ff0))
- 修改机构管理页面布局 ([ba52a6a](https://github.com/pure-admin/vue-pure-admin/commit/ba52a6a6a62ef77c8e9532379828b56ad454b4f6))

### [0.0.5](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.4...v0.0.5) (2025-04-16)

### Bug Fixes

- 表格样式调整 ([48b7e0b](https://github.com/pure-admin/vue-pure-admin/commit/48b7e0bd611f70a0e2b0a9dc9bb0bb324338a233))
- 调整领队讲师上传展示 ([9bc7ec7](https://github.com/pure-admin/vue-pure-admin/commit/9bc7ec721ea067d2ce02b744a5cfa5b979973410))
- 订单详情调整 ([402daec](https://github.com/pure-admin/vue-pure-admin/commit/402daecf9ecc31f20732ef06db210e981dc87ead))
- 机构管理+订单管理，面包屑修改 ([87e0ac1](https://github.com/pure-admin/vue-pure-admin/commit/87e0ac1268ff810a8ea2767313e8098eaedb9864))
- 价格设置默认规格 ([ec21ff6](https://github.com/pure-admin/vue-pure-admin/commit/ec21ff6c42cdab92042c7aba3b6b57f0a55a2a36))
- 课程编辑按钮展示异常 ([39f207e](https://github.com/pure-admin/vue-pure-admin/commit/39f207ea1d3659952d733bea648522f9a61bb273))
- 课程管理和价格设置数量限制 ([cf86a70](https://github.com/pure-admin/vue-pure-admin/commit/cf86a7057be2cc846bc72f7d74c1b6aef28d98d6))
- 删除console.log ([ae01a54](https://github.com/pure-admin/vue-pure-admin/commit/ae01a54d3420884b94e6a082df0f11ca2aa81281))
- 添加code码提示 ([52f0ee4](https://github.com/pure-admin/vue-pure-admin/commit/52f0ee4267c9b56243a74978f6931b46db284921))
- 图标调整 ([9f2da2e](https://github.com/pure-admin/vue-pure-admin/commit/9f2da2e682c07ec49060fdfbe70526dc2aa47456))
- 修复登录回车不触发图形验证 ([b15b791](https://github.com/pure-admin/vue-pure-admin/commit/b15b791e7a2105f793e7b968c75a13ff387fa7ed))
- 修复修改密码失效问题 ([080e68b](https://github.com/pure-admin/vue-pure-admin/commit/080e68b4a4b8af66ae6598301ca2736c58d38ba8))
- 修改手机号调整 ([2794d72](https://github.com/pure-admin/vue-pure-admin/commit/2794d72af9001c618b2ecbe128ded24f3eb19f02))

### [0.0.4](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.3...v0.0.4) (2025-04-15)

### Features

- 添加token异常码提示 ([fd3260e](https://github.com/pure-admin/vue-pure-admin/commit/fd3260e6b65e2b0d1a473c6b4b2751619280b706))

### Bug Fixes

- 编辑不展示机构 ([120170d](https://github.com/pure-admin/vue-pure-admin/commit/120170d268447e4efec065a155e1dc2936ab8954))
- 编辑课期失败 ([e08ab27](https://github.com/pure-admin/vue-pure-admin/commit/e08ab2779d0d667f28a21c20cd78cfcbdef5473c))
- 复制当前期未显示未上架状态 ([c5d4b59](https://github.com/pure-admin/vue-pure-admin/commit/c5d4b5985db044d94a262f19156ff492a4409a1f))
- 基地编辑调整 ([9c774e2](https://github.com/pure-admin/vue-pure-admin/commit/9c774e2c4ea986e0e94709b95b4503edad2b62dc))
- 面包屑配置 ([8e08644](https://github.com/pure-admin/vue-pure-admin/commit/8e086442fd1995b05be79a7ced8855b350b81ebe))
- 使描述列表字段居中 ([af59828](https://github.com/pure-admin/vue-pure-admin/commit/af598288c19d0130f8d22afc628f399a8db8a2af))
- 新增行程点取消按钮变为返回按钮 ([3671729](https://github.com/pure-admin/vue-pure-admin/commit/3671729632471b1a09ca059c3382631d07560099))
- 修复财务查看关联订单没数据 ([9b4118c](https://github.com/pure-admin/vue-pure-admin/commit/9b4118c7945de02666d02c43bf7dc01a95f900dd))
- 学生情况家长显示 ([f67c93b](https://github.com/pure-admin/vue-pure-admin/commit/f67c93bc961d7fb15234b45852777dadfc77225f))

### [0.0.3](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.2...v0.0.3) (2025-04-15)

### Bug Fixes

- 创建时取消脱敏icon ([b090b53](https://github.com/pure-admin/vue-pure-admin/commit/b090b53df3b9a8685c47a56a04fec7057fee115c))
- 调整提示 ([4f4693f](https://github.com/pure-admin/vue-pure-admin/commit/4f4693f215cef079b177cbc19783653fd8a241b9))
- 关联订单调整 ([c36df1f](https://github.com/pure-admin/vue-pure-admin/commit/c36df1f63eaee16ab83adb30602850fb1624fdf6))
- 机构管理编辑修改 ([e73e0d9](https://github.com/pure-admin/vue-pure-admin/commit/e73e0d98dc49fc127dd1de0e29951fbd5f93b11c))
- 取消验证码必传 ([d755218](https://github.com/pure-admin/vue-pure-admin/commit/d7552189436efd4b6c11a5ef3572ef7a48acb2a0))
- 手机号调整 ([58d2b0d](https://github.com/pure-admin/vue-pure-admin/commit/58d2b0d2f6b9981e28caf0a429c8af1ee44d8bfa))
- 首页编辑调整 ([342144a](https://github.com/pure-admin/vue-pure-admin/commit/342144aeddc02602a4f6d8daad0ce7d06f8da4d5))
- 修复订单管理课程期号显示问题 ([2dcd78b](https://github.com/pure-admin/vue-pure-admin/commit/2dcd78b029a58977effc16808d06a86493ea5946))
- 修复丢失页面 ([f7abd44](https://github.com/pure-admin/vue-pure-admin/commit/f7abd4443b7db5adb44ed26cda3a21cf9384d37d))
- 修复领队讲师账号管理手机号验证 ([c2e837e](https://github.com/pure-admin/vue-pure-admin/commit/c2e837e4f00cab3d924f31f89fcf1e4c85817386))

### [0.0.2](https://github.com/pure-admin/vue-pure-admin/compare/v0.0.1...v0.0.2) (2025-04-14)

### 0.0.1 (2025-04-14)

### Features

- 当期详情页 ([d19bf4f](https://github.com/pure-admin/vue-pure-admin/commit/d19bf4fd5e20cd0885d90b26b185a9d2b441fffb))
- 调整视频介绍 ([3e214dc](https://github.com/pure-admin/vue-pure-admin/commit/3e214dcd4d2817c093a26d81bea4a0fcb96c370e))
- 价格设置 ([007baf4](https://github.com/pure-admin/vue-pure-admin/commit/007baf453f88aeb37a4ad9ea4fd3adfe6d7836a8))
- 价格设置 ([d86a49b](https://github.com/pure-admin/vue-pure-admin/commit/d86a49b2be3244f2f251585ecabd13f71f3b5ebd))
- 价格设置编辑费用和退款 ([ab9274f](https://github.com/pure-admin/vue-pure-admin/commit/ab9274f83105dcb3237aa68880988e61fa463f2c))
- 价格设置查询页 ([82acf5e](https://github.com/pure-admin/vue-pure-admin/commit/82acf5e60ea6c9c73f9231e6f838d31b74f2a649))
- 课程报告查询和编辑 ([4124099](https://github.com/pure-admin/vue-pure-admin/commit/4124099f2ef239461047047c6dc46f6a34e86ca9))
- 课程创建 ([9dc1be3](https://github.com/pure-admin/vue-pure-admin/commit/9dc1be393f0f0362ab75deaff40dae8ff01d4d75))
- 课程介绍页面 ([109d3ab](https://github.com/pure-admin/vue-pure-admin/commit/109d3ab407c4bbfb987c7a10c93d702ef9925d40))
- 上课跟踪 ([154256b](https://github.com/pure-admin/vue-pure-admin/commit/154256ba0560ae9237faaa483610b1ab0d57329e))
- 首页编辑添加验证码 ([80bd61c](https://github.com/pure-admin/vue-pure-admin/commit/80bd61c314c2ba7dc4a47e30718d7b3216efaf4d))
- 添加 Vue 组件树检查插件并更新依赖 ([be724bf](https://github.com/pure-admin/vue-pure-admin/commit/be724bf43a6626fa37eba18b7e0c2b0319420025))
- 添加登录图形验证 ([c2dfb65](https://github.com/pure-admin/vue-pure-admin/commit/c2dfb656e922b904c987ca4328ab585ea75feed2))
- 添加机构端角色权限 ([bbb560a](https://github.com/pure-admin/vue-pure-admin/commit/bbb560aa51b04fb7b4a1716e374c63524d52fd3c))
- 添加机构管理和基地管理模块的埋埋点 ([377c2a6](https://github.com/pure-admin/vue-pure-admin/commit/377c2a650eb33882f112d0d0c9b555cba89639ae))
- 添加机构名称显示 ([ea0c816](https://github.com/pure-admin/vue-pure-admin/commit/ea0c816ead6e4ac8a2727dd68c14dd277ec7493f))
- 添加讲师管理及详情页 ([3b2f836](https://github.com/pure-admin/vue-pure-admin/commit/3b2f83658d2fdae026ad2bd010418c6236e022c7))
- 添加路由 ([0bdd209](https://github.com/pure-admin/vue-pure-admin/commit/0bdd20961fe82409e7263227a952c854a9a473c2))
- 添加权限码 ([09dae34](https://github.com/pure-admin/vue-pure-admin/commit/09dae34ab2f476483c9880841442bf73e7f31939))
- 添加视频简介 ([321fe94](https://github.com/pure-admin/vue-pure-admin/commit/321fe94fa2c46e2ef97671e377f60456c59b4f68))
- 添加文件预览 ([0c07435](https://github.com/pure-admin/vue-pure-admin/commit/0c07435d7461032cb30f8a3d595af5d86338c155))
- 添加重置密码、调整手机验证、修改字段获取 ([d0262ac](https://github.com/pure-admin/vue-pure-admin/commit/d0262ac898e60ed07f58c8c628523b8915827b65))
- 添加token下线提示 ([14b0174](https://github.com/pure-admin/vue-pure-admin/commit/14b01741ff4734b1ca3c553442b421cfa2ff5284))
- 完善机构管理 ([4295660](https://github.com/pure-admin/vue-pure-admin/commit/42956603681daf39c0b75f2d9ba82a38b27d3d60))
- 微信绑定、登录时取消空格符、手机号验证调整、初始密码规则调整 ([30218bd](https://github.com/pure-admin/vue-pure-admin/commit/30218bda3670712c87e46b5cfd41f0f6c8c9d941))
- 文件上传相关文件 ([661627e](https://github.com/pure-admin/vue-pure-admin/commit/661627ee0e060e13b103b38c06858dc5e4b260ba))
- 新增财务导出+首页编辑 ([ecce347](https://github.com/pure-admin/vue-pure-admin/commit/ecce347ce55c7551ea999800beab1ddf4f9741aa))
- 新增财务页面+功能调整 ([f2bb75d](https://github.com/pure-admin/vue-pure-admin/commit/f2bb75d0dc3ce0228e020a5c9bda9868c620ac23))
- 新增订单管理详情页 ([77b9f3e](https://github.com/pure-admin/vue-pure-admin/commit/77b9f3ec28609393e0d7beb2141eb7e6a4a0d6ce))
- 新增订单管理页面 ([7f61802](https://github.com/pure-admin/vue-pure-admin/commit/7f61802da4977198e61ad38705cff18771f7b76c))
- 新增机构一句话简介 ([a5d5c32](https://github.com/pure-admin/vue-pure-admin/commit/a5d5c32a783d6c186918a1a13a2f1b5537017d19))
- 新增基地管理 ([0b629c4](https://github.com/pure-admin/vue-pure-admin/commit/0b629c4f11d4e3369757c054077eea24354d01e7))
- 新增基地管理与编辑 ([a80aa24](https://github.com/pure-admin/vue-pure-admin/commit/a80aa246c5891bb8d08ab21f2a1f50f921468fac))
- 新增课程列表 ([607e2d4](https://github.com/pure-admin/vue-pure-admin/commit/607e2d45f5d9e268ed6ea7227145907e7b61e83e))
- 新增首页、机构管理、基地管理页面 ([c103915](https://github.com/pure-admin/vue-pure-admin/commit/c1039156d8c43b595f8110e6ae6621698671b1f4))
- 新增团购分享静态及关闭团单 ([a6838c4](https://github.com/pure-admin/vue-pure-admin/commit/a6838c4e10644fd45609832e55f3ff61b5df9e52))
- 新增行程安排设置 ([a0eca47](https://github.com/pure-admin/vue-pure-admin/commit/a0eca472ce45f615f8d0bd8a3420293b50fff38c))
- 新增作业设计编辑 ([c54a6cc](https://github.com/pure-admin/vue-pure-admin/commit/c54a6cca5c13e3af7d8d90e8e9986cee12da50d0))
- 行程安排等切换 ([5a92467](https://github.com/pure-admin/vue-pure-admin/commit/5a92467807a94e04bf220ffe2bf2134bacd5369d))
- 行程安排删除 ([38111cf](https://github.com/pure-admin/vue-pure-admin/commit/38111cf1cef34f128e94966ad1783b8e725742a7))
- 修改领队/讲师/账号搜索加密、表单脱敏处理 ([72ab590](https://github.com/pure-admin/vue-pure-admin/commit/72ab5908d789d80207c0410741445faea356b217))
- 学生情况和关联订单及作业详情 ([9c173a8](https://github.com/pure-admin/vue-pure-admin/commit/9c173a8a95b25b6d1bef0c866e044aa02a04dd76))
- 用户评价及学生情况等页面 ([a99f478](https://github.com/pure-admin/vue-pure-admin/commit/a99f478468490c6087482e3fe62c1ebc861c71e4))
- 增加登录接口 ([6b12f04](https://github.com/pure-admin/vue-pure-admin/commit/6b12f04a2367101122c11fc1d6bdf25f8f88f606))
- 增加富文本编辑器 ([f6c8484](https://github.com/pure-admin/vue-pure-admin/commit/f6c848407eca6a47a0701729b43cac5b67a84da4))
- 增加图像验证码校验 ([7b25c4a](https://github.com/pure-admin/vue-pure-admin/commit/7b25c4a700efab9344e1e937c30e2099b57b29b2))
- 增加图像验证码校验 ([8c511cd](https://github.com/pure-admin/vue-pure-admin/commit/8c511cd969c400d66e6ab3d9600c7679e7c94cef))
- 重构重置密码及课程分类 ([90bf2ae](https://github.com/pure-admin/vue-pure-admin/commit/90bf2aee448a2bcc339cb937e8240bd59460cf3d))
- **account:** 获取角色列表并优化搜索功能 ([75a5d48](https://github.com/pure-admin/vue-pure-admin/commit/75a5d48ab8432079b6b18d905627744dd5be5db8))
- **account:** 手机号码输入和验证 ([d3786a7](https://github.com/pure-admin/vue-pure-admin/commit/d3786a72f56e6f2918d9684455737a67c20a77ec))
- **account:** 手机搜索加密、修改表格字段 ([3c5c715](https://github.com/pure-admin/vue-pure-admin/commit/3c5c7153649d940ce33a25e61399da3fd0fad758))
- **account:** 添加创建账号功能 ([9febab3](https://github.com/pure-admin/vue-pure-admin/commit/9febab3524fb25c78cce0b88c5daa046f51801cb))
- **account:** 添加讲师详情页面的新增资质文件和编辑信息页面 ([3980f56](https://github.com/pure-admin/vue-pure-admin/commit/3980f56291db977445f6a43b17c2cdfb1b07aea2))
- **account:** 添加手机号验证码功能优化验证码发送逻增加手机号格式验证,优化教师详情页面，支持不同角色的页面跳转 ([c915497](https://github.com/pure-admin/vue-pure-admin/commit/c915497a84d267cc2685f6894d86e5d51952890f))
- **account:** 添加账号详情页面重构冻结/解冻操作流程，增加确认对话框 ([dda432f](https://github.com/pure-admin/vue-pure-admin/commit/dda432f1b496971057b8093bb9105f35330849bc))
- **account:** 添加账户详情页面 ([abdbe59](https://github.com/pure-admin/vue-pure-admin/commit/abdbe59dccec1b7cab35bd94c61239eda7f70942))
- **account:** 新增领队讲师课期功能并优化账号管理,新增领队讲师课期查询功能 ([16b002a](https://github.com/pure-admin/vue-pure-admin/commit/16b002a8174af24d3cd329453d1b3c1eb7215df8))
- **account:** 新增账号管理、讲师管理和领队管理功能 ([f15f9fd](https://github.com/pure-admin/vue-pure-admin/commit/f15f9fd2e986563577a4d5bd622e181ee73e2418))
- **account:** 新增账号管理编辑功能 ([d758916](https://github.com/pure-admin/vue-pure-admin/commit/d758916cf1ab7b95980934d5194aafa5476ac5fa))
- **account:** 优化账号管理,新增角色列表修复账号创建和编辑,优化账号详情统一时间选择器的处理方式 ([ee4c546](https://github.com/pure-admin/vue-pure-admin/commit/ee4c54648c830426d351984e4ba7c12921f5a4fb))
- **account:** 优化账号管理功能,新增领队讲师列表详情接口和相关页面功能,调整资质文件上传 ([3fd0890](https://github.com/pure-admin/vue-pure-admin/commit/3fd0890119838d9645bf94ccc2fc799328c389a5))
- **account:** 账号创建手机验证、上传列表展示 ([3cf3742](https://github.com/pure-admin/vue-pure-admin/commit/3cf3742db581639f60aca5e021a157a797e82196))
- **account:** 账号创建手机验证、上传列表展示、角色表单添加、二维码展示 ([b3c98d3](https://github.com/pure-admin/vue-pure-admin/commit/b3c98d3b70e4dabec25f3f786d99623e789389b9))
- **account:** 账号管理，新增账号，查询、创建、冻结账号等功能添加 SM4 加密解密工具 ([f129bee](https://github.com/pure-admin/vue-pure-admin/commit/f129beef1436f0af9296ace4f3e4d3fc7c7b594c))
- **account:** 整合领队讲师分页列表查询 ([2392069](https://github.com/pure-admin/vue-pure-admin/commit/23920697a1e46759d83deee755c6ec64a773fc53))
- add login api ([640f4f2](https://github.com/pure-admin/vue-pure-admin/commit/640f4f25bea343810537b92d110e01e4a212eaaa))

### Bug Fixes

- 本地存储用户信息及价格设置页面调整 ([b7e98ed](https://github.com/pure-admin/vue-pure-admin/commit/b7e98ed55b8c7f2d2a03475e49e81c53bbf4704c))
- 财务导出+文件预览+订单详细调整 ([3be96f9](https://github.com/pure-admin/vue-pure-admin/commit/3be96f98f0ec99998ae4caecb58147f69cfe00d9))
- 侧边栏点击每一项最后一个子集图标消失问题 ([a00099d](https://github.com/pure-admin/vue-pure-admin/commit/a00099deb8bb2e07cede33e783a6a4ba969a8029))
- 侧边栏图标 ([1e29168](https://github.com/pure-admin/vue-pure-admin/commit/1e291689bb06133ff78632f2c8190648d0aec493))
- 创建课期后编辑按钮消失 ([230f9bd](https://github.com/pure-admin/vue-pure-admin/commit/230f9bd5337b9220bb4b0bc56a991e93501f4a80))
- 创建课期后loading状态关闭 ([fa1bf0e](https://github.com/pure-admin/vue-pure-admin/commit/fa1bf0ebaa17ee662527ab6025acccf25ea344a2))
- 从当前期复制 ([6fa8b90](https://github.com/pure-admin/vue-pure-admin/commit/6fa8b90a2c07a2ebff298ac73b4dc5685e369aea))
- 存储用户数据 ([0c09de3](https://github.com/pure-admin/vue-pure-admin/commit/0c09de30e8fd17ca91f3e60f3d1b9ab1318aec49))
- 登录页调整 ([2efeb80](https://github.com/pure-admin/vue-pure-admin/commit/2efeb80dd09dd675a5eccc892c86db6d66cc2cc0))
- 调整登录文案 ([cf5bfd0](https://github.com/pure-admin/vue-pure-admin/commit/cf5bfd04da269db162698c633dbcdaec72c86121))
- 调整订单页面 ([bc725e6](https://github.com/pure-admin/vue-pure-admin/commit/bc725e6c8d249bb0d8e414d0ee741a49b0312164))
- 调整讲师领队编辑信息 ([87abe8b](https://github.com/pure-admin/vue-pure-admin/commit/87abe8be2121ef3848133cd4ed4bd069c2c590b6))
- 调整领队、讲师冻结功能 ([c7bdfc8](https://github.com/pure-admin/vue-pure-admin/commit/c7bdfc83d225b8fa09037392834676e4a1c10063))
- 调整领队、讲师详情课期 ([50ed16a](https://github.com/pure-admin/vue-pure-admin/commit/50ed16a001b3c7102a893491c7eb4243b98b834d))
- 调整默认接口域名 ([4257e69](https://github.com/pure-admin/vue-pure-admin/commit/4257e695eb466c703bc13a2fc1d5bb5ca47c46d3))
- 调整手机号验证 ([55f99ee](https://github.com/pure-admin/vue-pure-admin/commit/55f99ee4aed06d90b7f1fb392800fabc0ed2d0f4))
- 调整异常提示 ([32f8618](https://github.com/pure-admin/vue-pure-admin/commit/32f861804c887f5af4e6d4e188cd9412ae9be1d9))
- 调整重置密码及首页头部机构样式 ([ef6e844](https://github.com/pure-admin/vue-pure-admin/commit/ef6e8446a5b38d556251cb2a881f63c780e2fbe7))
- 调整code码 ([d5d869f](https://github.com/pure-admin/vue-pure-admin/commit/d5d869f7bbbdf89af2aa21f355f681581eb77ec9))
- 订单详情+首页调整 ([c195f21](https://github.com/pure-admin/vue-pure-admin/commit/c195f214419010b33f5335cc68831eae3ebf892b))
- 订单详情退单功能调整+首页编辑样式调整 ([2653382](https://github.com/pure-admin/vue-pure-admin/commit/265338275a87bdff1f0266037be400529b6fbf44))
- 订单详情页调整 ([47ac07b](https://github.com/pure-admin/vue-pure-admin/commit/47ac07b1c8c58c43df8268af9d287494c757b235))
- 二次提交 ([09366d2](https://github.com/pure-admin/vue-pure-admin/commit/09366d2dca9f1d24494690aff0c59a5139da0200))
- 非下架状态不显示编辑按钮及课期删除 ([b3ae773](https://github.com/pure-admin/vue-pure-admin/commit/b3ae773fe6d6f84ae1c859fbb6f5d0904c9b37e4))
- 分类接口 ([44a73ac](https://github.com/pure-admin/vue-pure-admin/commit/44a73ac2eb4f4ef30c9510a745c6bf16a7efe8ef))
- 复制创建课期调整及课期编辑 ([585a6c3](https://github.com/pure-admin/vue-pure-admin/commit/585a6c3ed8e0c22594705a0249f49ed1fcc865bc))
- 复制课期列表调整 ([5ef574a](https://github.com/pure-admin/vue-pure-admin/commit/5ef574adaa80aa02881ea40011186f8593c2cc2f))
- 富文本编辑样式调整 ([38ae4d0](https://github.com/pure-admin/vue-pure-admin/commit/38ae4d0443a0881ceb7efe43b59506162a29cf9f))
- 关联课程及关联订单 ([daf7465](https://github.com/pure-admin/vue-pure-admin/commit/daf74651c085e8e20c142d052428ef363fcb8ef6))
- 合并冲突 ([1dba68f](https://github.com/pure-admin/vue-pure-admin/commit/1dba68fa5e64eee52ae14e18e3d983a719317c6b))
- 机构编辑调整 ([774ddb3](https://github.com/pure-admin/vue-pure-admin/commit/774ddb37373dc06162e2e51037e532fe903339ca))
- 机构管理上传调整 ([f21b22a](https://github.com/pure-admin/vue-pure-admin/commit/f21b22ac94fb23a33495ea4d090d3f0c0b1eac03))
- 基地管理，机构管理，日志管理，订单管理样式调整 ([5c06fd1](https://github.com/pure-admin/vue-pure-admin/commit/5c06fd1ab101fc645ac7108e65fa90fb36dd8db1))
- 基地管理调整 ([72ad3d9](https://github.com/pure-admin/vue-pure-admin/commit/72ad3d9da447f5685bdcea07f16729ae8a5c549b))
- 基地管理调整 ([c956561](https://github.com/pure-admin/vue-pure-admin/commit/c9565611439b466d15af23d26a731423736eb1c8))
- 基地管理调整 ([46d7c58](https://github.com/pure-admin/vue-pure-admin/commit/46d7c584e5da0113f603d067779ed7b8beb203e4))
- 基地管理样式调整 ([ceee9b6](https://github.com/pure-admin/vue-pure-admin/commit/ceee9b6ace61ee1342e0e1376b628a568c444dfc))
- 价格设置编辑标识 ([3581361](https://github.com/pure-admin/vue-pure-admin/commit/3581361cb9ab232b48650d5980868e89052a5cee))
- 价格设置调整 ([3628615](https://github.com/pure-admin/vue-pure-admin/commit/36286151604f8077fda1b6191601e7ed21359e33))
- 开启权限 ([1d0a7d0](https://github.com/pure-admin/vue-pure-admin/commit/1d0a7d0035dc6880a779d926e35c1de5c741b699))
- 课程编辑调整 ([7b3a718](https://github.com/pure-admin/vue-pure-admin/commit/7b3a71860248566543a005fc1b53d680e7de072a))
- 课程分类调整 ([561b70f](https://github.com/pure-admin/vue-pure-admin/commit/561b70f79f83a7fb23e34c49e67f6ea8620142ee))
- 课程分类无数据 ([51724d2](https://github.com/pure-admin/vue-pure-admin/commit/51724d25eaabd0d0ffb9be6d177666c89cb3d923))
- 课程基本信息调整 ([774318b](https://github.com/pure-admin/vue-pure-admin/commit/774318b2a60bee17d3eae4b0017710346c2aabce))
- 课程介绍显示调整 ([4787e64](https://github.com/pure-admin/vue-pure-admin/commit/4787e6428fc06c88b7796a6c64ce5ff88915dd40))
- 课程课期创建人数限制报错 ([6452e3f](https://github.com/pure-admin/vue-pure-admin/commit/6452e3f33cdb0c73e3df59e4ceda61265ae409db))
- 课程列表及签名调整 ([512082f](https://github.com/pure-admin/vue-pure-admin/commit/512082f245600aa2be5d77f1648fb0b9a2e90541))
- 课程详情（介绍，知识点，装备说明，注意事项）调整 ([a1b6c6e](https://github.com/pure-admin/vue-pure-admin/commit/a1b6c6ef1e952ee5f1651471b7005b2789fc95b0))
- 课程详情调整 ([673c454](https://github.com/pure-admin/vue-pure-admin/commit/673c4541277e9044f92df9d8b9864b2455297bda))
- 课程详情调整 ([6439176](https://github.com/pure-admin/vue-pure-admin/commit/6439176d70ff2ec158ba0b99643f46239116c7a3))
- 课程详情页面 ([1ea93ff](https://github.com/pure-admin/vue-pure-admin/commit/1ea93ff7b7fa6abee612d0833bfe4c6a4a414322))
- 课期编辑限制 ([06f8544](https://github.com/pure-admin/vue-pure-admin/commit/06f85441c7db381b590f06579ebd154f845456b2))
- 课期创建后跳转页面 ([753167e](https://github.com/pure-admin/vue-pure-admin/commit/753167ea7b0a0b62912b2cb83f17360f858f00e0))
- 课期创建日志 ([ac9eeaf](https://github.com/pure-admin/vue-pure-admin/commit/ac9eeafd5843b988c7a13fdc916d393dfcdfff1b))
- 课期创建校验 ([207df61](https://github.com/pure-admin/vue-pure-admin/commit/207df619c2be64ab03b9eba5a4caa86d98fc4bb1))
- 课期创建选择时间调整 ([70a6842](https://github.com/pure-admin/vue-pure-admin/commit/70a684267591750addded0ab3678e8281cea3116))
- 课期基本信息编辑调整 ([82400f6](https://github.com/pure-admin/vue-pure-admin/commit/82400f689e584814f183e7e333758b92367c7db5))
- 课期接口调整 ([8e5937b](https://github.com/pure-admin/vue-pure-admin/commit/8e5937b79dff39113144c7523c594a3f08078419))
- 课期全新创建限制创建一个课期及未选上课时间提示及报名时间0.5小时增加 ([5d00c5c](https://github.com/pure-admin/vue-pure-admin/commit/5d00c5c579e6ccb2f688def5d88a46bcaa3d8e76))
- 课期详情上下架处理 ([04cf77c](https://github.com/pure-admin/vue-pure-admin/commit/04cf77cd6077e8c59b73ea4282fa40ac732f6f0c))
- 课期修改开课时间无效 ([8e466f4](https://github.com/pure-admin/vue-pure-admin/commit/8e466f47f4df8fb248031c223a38b0c5a19453aa))
- 课期状态及弹框显示 ([79068d9](https://github.com/pure-admin/vue-pure-admin/commit/79068d9a7064516f1172222af2e58136b022c53a))
- 领队管理，账号管理脱敏处理 ([bf89bd1](https://github.com/pure-admin/vue-pure-admin/commit/bf89bd1cee7c689b9d5ea29b6b98ecf733152d4d))
- 路由调整 ([f059374](https://github.com/pure-admin/vue-pure-admin/commit/f0593743168f82a305b06f54f16417389d21a563))
- 请求头调整 ([8451a67](https://github.com/pure-admin/vue-pure-admin/commit/8451a67eee3cfc3179192c14269663e7d0ae1784))
- 筛选调整 ([0c5b98e](https://github.com/pure-admin/vue-pure-admin/commit/0c5b98ec4d85a0e86cb8ee4132479c4e3ec4f4cf))
- 审核状态调整 ([0ed040d](https://github.com/pure-admin/vue-pure-admin/commit/0ed040dda511ecc2d6ce5161387ffee9609bd86a))
- 首页编辑新增文件上传 ([da4390c](https://github.com/pure-admin/vue-pure-admin/commit/da4390c351a145c1a32fa614f2dafe0c0501c67f))
- 首页调整 ([c1d0ee5](https://github.com/pure-admin/vue-pure-admin/commit/c1d0ee5c602e139c3b5e6051c31e4675ffe3349a))
- 首页图标调整 ([a5bcff0](https://github.com/pure-admin/vue-pure-admin/commit/a5bcff01fed44b1d4b055caae7361798cacbe34f))
- 首页样式调整 ([d4c39eb](https://github.com/pure-admin/vue-pure-admin/commit/d4c39eb08c43855ff7f99efb8fc6f85659572735))
- 首页页面调整 ([b730051](https://github.com/pure-admin/vue-pure-admin/commit/b730051e2c3b090f754b4ce9f6c4ba6fc1d621f7))
- 首页字体修改 ([43ccb8d](https://github.com/pure-admin/vue-pure-admin/commit/43ccb8dac5456a629c0454411e3cf359b534ecbc))
- 首页UI调整 ([fbe2f6e](https://github.com/pure-admin/vue-pure-admin/commit/fbe2f6e34a0844d72ce6e7dd9dd27d7b212b6c12))
- 输入空格调整及按钮调整 ([9d37513](https://github.com/pure-admin/vue-pure-admin/commit/9d375137e2d32fc52e0181ba6b8da9a6f0b4e34c))
- 思政机构端标题文案 ([ffe2a77](https://github.com/pure-admin/vue-pure-admin/commit/ffe2a775c3651b641095c7ee28f7136a20aca3fc))
- 添加创建时机构显示 ([c30f36c](https://github.com/pure-admin/vue-pure-admin/commit/c30f36caecf74509f31e57137758eabeba719395))
- 添加默认机构展示 ([2e080dc](https://github.com/pure-admin/vue-pure-admin/commit/2e080dc2925cd5dc9db6c799b7d60689276602c2))
- 团购二维码相关 ([0ec4e80](https://github.com/pure-admin/vue-pure-admin/commit/0ec4e80dca657790a85b304916373e19b172ac03))
- 团购分享调整 ([9e9b4dc](https://github.com/pure-admin/vue-pure-admin/commit/9e9b4dce3aa1ffb06e9370465b6cc61ef4d743b1))
- 退单二次确认调整 ([a19680b](https://github.com/pure-admin/vue-pure-admin/commit/a19680b58491f11f521bc7fe193ec4ee54156c6f))
- 文件路径调整 ([cea3144](https://github.com/pure-admin/vue-pure-admin/commit/cea31442f0dc53aa7170110d2ca12fcab8188eeb))
- 文件预览和财务导出调整 ([38be180](https://github.com/pure-admin/vue-pure-admin/commit/38be180fd2955d6eade3a55f83c72fe4244d3b46))
- 文件预览路径调整 ([6fd6515](https://github.com/pure-admin/vue-pure-admin/commit/6fd6515f1b9ad30be5d5304f26c094307b47517b))
- 文件预览图标 ([f993829](https://github.com/pure-admin/vue-pure-admin/commit/f99382908c39bdc379e3219834cd2c0d1f0c4432))
- 新建课期时间复制功能调整 ([927db92](https://github.com/pure-admin/vue-pure-admin/commit/927db92b54222124bd61e0d2fb3a73d45c01eeb7))
- 新增课期调整 ([9e04151](https://github.com/pure-admin/vue-pure-admin/commit/9e041518a428e1a66def0cfadc99d55f009477cb))
- 行程安排富文本编辑 ([a2b72ea](https://github.com/pure-admin/vue-pure-admin/commit/a2b72eab5fd07100100fb94440a7c4a6d1ff1451))
- 行程安排空状态处理 ([5328a12](https://github.com/pure-admin/vue-pure-admin/commit/5328a12d766759843db507dc7270600f8cae3d17))
- 修复手机验证 ([4bcb86d](https://github.com/pure-admin/vue-pure-admin/commit/4bcb86d1a84d3336c1e03434c7417191ce054986))
- 修改端口号 ([2f12922](https://github.com/pure-admin/vue-pure-admin/commit/2f129222fb2b51250e4464e1fcc67f934c20c818))
- 修改文件名 ([ff45d9e](https://github.com/pure-admin/vue-pure-admin/commit/ff45d9efea490ae731878cec3de4cb588182a472))
- 页面调整 ([c0d5b0b](https://github.com/pure-admin/vue-pure-admin/commit/c0d5b0b1dac51e7bcd6de9dabdb9bcb088dab7ea))
- 页面调整 ([bd20eb2](https://github.com/pure-admin/vue-pure-admin/commit/bd20eb2776597270c77aaaca2314acd6a9f23076))
- 页面内边距调整及行程管理滚动条 ([77e963e](https://github.com/pure-admin/vue-pure-admin/commit/77e963ebae17e0ae9cf16fb219ffe441963c9c0a))
- 页面跳转 ([5356b05](https://github.com/pure-admin/vue-pure-admin/commit/5356b056ccf2e404f6b2c766d6aa14dc39fe470d))
- 页面样式调整 ([48db257](https://github.com/pure-admin/vue-pure-admin/commit/48db257abd8fcf7d095478c7de4b126ddcd3798c))
- 隐藏侧边栏和课程创建文案调整 ([8f111ee](https://github.com/pure-admin/vue-pure-admin/commit/8f111ee2e2afacd2ec00ad947d44885623fa69e3))
- 隐藏系统默认角色删除 ([136697c](https://github.com/pure-admin/vue-pure-admin/commit/136697cc995f75f72170f75b790ada00695801ab))
- 隐藏账户设置 ([dd56a0f](https://github.com/pure-admin/vue-pure-admin/commit/dd56a0f49815a3953f4de44b277eb7d24b95fe2a))
- 隐藏子订单号 ([8453034](https://github.com/pure-admin/vue-pure-admin/commit/845303430590c6954925893fe189e2e738c8dfce))
- 隐藏hideTabs ([49ddfff](https://github.com/pure-admin/vue-pure-admin/commit/49ddfffe5899c64f09aa00f5644c6abf6a86649d))
- 用户评价按钮样式 ([7d60417](https://github.com/pure-admin/vue-pure-admin/commit/7d60417c285de4255933597a6d7fc2d9b2835051))
- 用户评价等接口 ([fcd3733](https://github.com/pure-admin/vue-pure-admin/commit/fcd37338ea020ca21a925c650f24a7d157c8753d))
- 用户评价调整 ([9a18c9f](https://github.com/pure-admin/vue-pure-admin/commit/9a18c9f36954fc75296ac8e74b1f9c6f78dbdcb9))
- 用户评价和作业配图 ([7483b93](https://github.com/pure-admin/vue-pure-admin/commit/7483b93d082b10406523e7a092337958c7d1e0cc))
- 优化价格设置 ([4c56887](https://github.com/pure-admin/vue-pure-admin/commit/4c56887c1db6482c8b24a0084e1e44b69cb63e83))
- 预览与下载自定义指令 ([6943b8c](https://github.com/pure-admin/vue-pure-admin/commit/6943b8c8d886c3fb3b1c89ee862fd9b5963bed87))
- 重新推送 ([12c67a4](https://github.com/pure-admin/vue-pure-admin/commit/12c67a467626650f65ece7a04fa9a6edc718ebb2))
- 重置密码调整 ([2ae54cb](https://github.com/pure-admin/vue-pure-admin/commit/2ae54cbae63ff81708144f1e9a3baafaca6ec9c9))
- 重置密码调整+空状态调整 ([7349685](https://github.com/pure-admin/vue-pure-admin/commit/7349685616cc079e14aa4a08c54d8f43c888dfb1))
- 作业设计接口 ([2a2cce9](https://github.com/pure-admin/vue-pure-admin/commit/2a2cce9efdefbd3ff290238c9f00b3fecfa23446))
- 作业设计添加图片预览 ([d4934d8](https://github.com/pure-admin/vue-pure-admin/commit/d4934d8fc2297d62d61a86ce4b40ff79b483c1af))
- **account:** 优化账号创建页面布局和功能 ([268cbb5](https://github.com/pure-admin/vue-pure-admin/commit/268cbb53633fe27033f9b06be27bd6190420c1b2))
- **account:** 优化账户冻结/解冻功能 ([25d8d5b](https://github.com/pure-admin/vue-pure-admin/commit/25d8d5b0c819ade61f8bc63930297c0e07695351))
- html文件语言调整 ([4c4caef](https://github.com/pure-admin/vue-pure-admin/commit/4c4caef85b0a90608cb205c44ac3ef843e7cb673))
- token调整 ([806737e](https://github.com/pure-admin/vue-pure-admin/commit/806737e94363520cc417e50894d2791593f1e59a))

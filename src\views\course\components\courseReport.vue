<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { findCourseReportId } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { courseStore } from "@/store/modules/course.js";
const router = useRouter();
const route = useRoute();
const evaluateValue = ref("");
const resultValue = ref("");
onMounted(() => {
  getTableList();
});
// 获取信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: Number(route.query.periodId)
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await to(findCourseReportId(paramsData));
  // console.log("🎁-----result222-----", result);
  if (result?.code === 200) {
    resultValue.value = result?.data?.achievementSummary;
    evaluateValue.value = result?.data?.performanceEvaluation;
    if (result?.data?.performanceEvaluation) {
      courseStore().saveEvaluate(result?.data?.performanceEvaluation);
    } else {
      courseStore().saveEvaluate("");
    }
    if (result?.data?.achievementSummary) {
      courseStore().saveResult(result?.data?.achievementSummary);
    } else {
      courseStore().saveResult("");
    }
  } else {
    // ElMessage.error(err);
    console.log("😭-----err-----", err);
  }
  getListLoading.value = false;
};
</script>

<template>
  <div class="containers">
    <div class="content">
      <div class="buttons">
        <!-- <div
          class="create"
          @click="
            router.push({
              path: '/course/currentDetails/reportEdite',
              query: { periodId: route.query.periodId }
            })
          "
        >
          {{ "编辑报告" }}
        </div> -->
        <el-button
          v-if="courseStore().periodComplete === true"
          type="primary"
          @click="
            router.push({
              path: '/course/currentDetails/reportEdite',
              query: { periodId: route.query.periodId }
            })
          "
        >
          {{ "编辑报告" }}
        </el-button>
      </div>
      <div class="free">
        <div class="title">学生整体表现评价</div>
        <div class="text-box">
          <!-- <el-input
            v-model="evaluateValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="evaluateValue ? evaluateValue : '暂无数据'"
          /> -->
          {{ evaluateValue || "暂无数据" }}
        </div>
      </div>
      <div class="refund">
        <div class="title">教学成果总结</div>
        <div class="text-box">
          <!-- <el-input
            v-model="resultValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="resultValue ? resultValue : '暂无数据'"
          /> -->
          {{ resultValue || "暂无数据" }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow-y: auto;
  .content {
    // display: flex;
    box-sizing: border-box;
    padding: 0 0 30px 0;
    overflow-y: auto;
    .text-box {
      border: 1px solid #e5e5e5;
      border-radius: 3px;
      width: 100%;
      height: 180px;
      padding: 5px 10px;
      font-size: 14px;
      overflow-y: auto;
    }
    .buttons {
      display: flex;
      justify-content: flex-end;
      // width: 95%;
      // margin-top: 28vh;

      .create {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 36px;
        color: rgb(255 255 255 / 100%);
        cursor: pointer;
        background-color: rgb(64 149 229 / 100%);
        border-radius: 6px;
      }
    }

    .free {
      width: 100%;
      margin-bottom: 35px;
      // margin-top: 10px;
    }

    .title {
      margin-bottom: 20px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }

    .refund {
      width: 100%;
    }

    .text {
      width: 100%;
    }
  }
  .content::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景 */
  }

  .content::-webkit-scrollbar-thumb {
    background: #afafaf; /* 滚动条滑块颜色 */
  }

  .content::-webkit-scrollbar-thumb:hover {
    background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
  }
}
</style>

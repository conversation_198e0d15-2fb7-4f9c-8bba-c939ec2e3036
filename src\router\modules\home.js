import { $t } from "@/plugins/i18n";
import { home } from "@/router/enums";
import HomeIcon from "@/assets/home/<USER>";
import HomeIconActive from "@/assets/home/<USER>";
import { hoCodesList, reCodesList } from "@/router/accidCode.js";
const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/welcome",
  meta: {
    icon: "ep:home-filled",
    imgIcon: HomeIcon,
    imgIconActive: HomeIconActive,
    // imgIconActive: HomeIcon,
    title: $t("menus.pureHome"),
    rank: home,
    idCode: hoCodesList.baseCode
  },
  children: [
    {
      path: "/welcome",
      name: "Welcome",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: $t("menus.pureHome"),
        showLink: VITE_HIDE_HOME === "true" ? false : true,
        idCode: hoCodesList.pending
      }
    },
    {
      path: "/welcomea",
      redirect: "/welcome",
      meta: {
        title: $t("menus.pureHome"),
        showLink: VITE_HIDE_HOME === "true" ? false : true,
        idCode: hoCodesList.pending
      },
      children: [
        {
          path: "/welcome",
          name: "Welcome",
          component: () => import("@/views/welcome/index.vue"),
          meta: {
            title: $t("menus.pureHome"),
            idCode: hoCodesList.pending,
            showLink: false
          }
        },
        {
          path: "/welcome/homeEdit",
          name: "homeEdit",
          component: () => import("@/views/welcome/homeEdit.vue"),
          meta: {
            title: "编辑信息",
            idCode: reCodesList.homeEdit,
            showLink: false
          }
        },
        {
          path: "/welcome/changePassword",
          name: "changePassword",
          component: () => import("@/views/welcome/changePassword.vue"),
          meta: {
            title: "修改密码",
            idCode: reCodesList.changePassword,
            showLink: false
          }
        }
      ]
    }
  ]
};

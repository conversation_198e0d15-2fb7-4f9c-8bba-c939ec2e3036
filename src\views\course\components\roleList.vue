<script setup>
import { ref, onMounted, onActivated, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  accountAll,
  isaccFreeze,
  roleList,
  batchAssignRole
} from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";
import { courseStore } from "@/store/modules/course.js";
import { to, isEmpty } from "@iceywu/utils";
import { useTripStore } from "@/store/modules/trip.js";
defineOptions({
  name: "AccountManage"
});
const tripStore = useTripStore();
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const tableRef = ref();
onMounted(() => {
  getTableList();
  getroleList(); //获取角色列表(不分页)
  form.value.roleId = "all";
});
onActivated(() => {
  getTableList();
  getroleList(); //获取角色列表(不分页)
});
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  phone: "",
  roleId: "all"
});
// 获取角色列表(不分页)
const roleLists = ref([]);
const getroleList = async () => {
  const [err, result] = await requestTo(roleList());
  if (result) {
    // console.log('🍭result------------------------------>',result);
    // 处理数据
    let res = result.map(role => ({
      id: role.id,
      list_name: role.name
    }));
    if (route.query.roleId === "2") {
      roleLists.value = res.filter(item => item.id !== 2);
      // 添加"全部"选项
      roleLists.value = [
        {
          id: "all",
          list_name: "全部"
        },
        ...roleLists.value
      ];
    } else {
      roleLists.value = res.filter(item => item.id !== 3);
      // 添加"全部"选项
      roleLists.value = [
        {
          id: "all",
          list_name: "全部"
        },
        ...roleLists.value
      ];
    }
  }
};

// 表格数据
const tableData = ref([
  // {
  //   id: 0,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "1223",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      // 跳过roleId=all的情况，不发送该参数
      if (paramsDataKey === "roleId" && form.value[paramsDataKey] === "all") {
        continue;
      }
      if (form.value[paramsDataKey]) {
        // 仅对 phone 进行加密
        paramsData[paramsDataKey] = paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }
  //   console.log("🍧-----paramsData-----", paramsData);
  if (route.query.roleId === "2") {
    paramsData.filterRoleId = 2;
  } else {
    paramsData.filterRoleId = 3;
  }
  // return;
  const [err, result] = await requestTo(accountAll(paramsData));
  // console.log("🎁-账号管理----result-----", result);
  if (result) {
    result?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
    });
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };

//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = e => {
  form.value.startTime = e ? new Date(e[0])?.getTime() : "";
  form.value.endTime = e ? new Date(e[1])?.getTime() + (******** - 1) : "";
};

const value1 = ref([]);
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  form.value.roleId = "all";
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
const selectRoleListId = ref([]);
const selectRoleListName = ref("");
// 选择赋予角色
const handleSelectionChange = val => {
  if (route.query.roleId === "2") {
    if (val.length > 1) {
      ElMessage.error("只能选择一个角色");
      // 只保留第一个选中的角色
      const firstSelected = val[0];
      // 清除所有选中状态
      tableRef.value?.clearSelection();
      // 在下一个DOM更新周期重新选中第一个
      nextTick(() => {
        tableRef.value?.toggleRowSelection(firstSelected, true);
      });
      return;
    }
  }
  selectRoleListId.value = val.map(item => item.id);

  selectRoleListName.value = val.map(item => item.name).join("、");
  //   console.log('🦄  selectRoleListId.value------------------------------>',selectRoleListId.value, selectRoleListName.value);
};
const submitLoading = ref(false);
// 确认角色赋予
const submitForm = async () => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  const params = {
    adminIds: selectRoleListId.value || []
  };
  if (route.query.roleId === "2") {
    params.roleId = 2;
  } else {
    params.roleId = 3;
  }
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType:
      route.query.roleId === "2"
        ? `赋予了${selectRoleListName.value}讲师角色`
        : `赋予了${selectRoleListName.value}领队角色`
  };
  const [err, res] = await to(batchAssignRole(params, operateLog));
  if (res.code === 200) {
    ElMessage.success("赋予角色成功");
    // 确保包含角色的完整信息，包括姓名等
    let resList = res.data.map(it => ({
      id: it.id,
      name: it.name,
      // 添加可能需要的其他字段
      account: it.account,
      phone: it.phone,
      phoneCt: it.phoneCt,
      roles: it.roles
    }));

    // 只更新当前正在操作的角色类型
    if (route.query.roleId === "2") {
      // 获取当前store中已有的讲师角色
      const existingLecturers = useCourseStore.lecturerInfo || [];
      // 创建一个Map用于去重
      const lecturerMap = new Map();

      // 先添加已有角色
      existingLecturers.forEach(lecturer => {
        lecturerMap.set(lecturer.id, lecturer);
      });

      // 再添加新角色，如有重复会覆盖旧值
      resList.forEach(lecturer => {
        lecturerMap.set(lecturer.id, lecturer);
      });

      // 转换回数组并保存
      const mergedLecturers = Array.from(lecturerMap.values());
      useCourseStore.saveLecturerInfo(mergedLecturers);
      if (!isEmpty(route.query.lecturerIndex)) {
        tripStore.saveLecturerData({
          id: resList[0].id,
          name: resList[0].name
        });
      }
    } else {
      // 处理领队角色
      const existingLeaders = useCourseStore.leaderInfo || [];
      const leaderMap = new Map();

      existingLeaders.forEach(leader => {
        leaderMap.set(leader.id, leader);
      });

      resList.forEach(leader => {
        leaderMap.set(leader.id, leader);
      });

      const mergedLeaders = Array.from(leaderMap.values());
      useCourseStore.saveLeaderInfo(mergedLeaders);
    }
    let createName = "";
    if (!isEmpty(route.query.lecturerIndex)) {
      createName = "createTrip";
    } else {
      createName = "create";
    }
    if (
      route.query.type === "create" ||
      route.query.type === "draft" ||
      route.query?.type === "createPeriod"
    ) {
      router.replace({
        path: "/course/courseCreate",
        query: {
          type: route.query.type,
          copyId: route.query.copyId,
          courseId: route.query.courseId,
          id: route.query.id,
          create: createName,
          draftId: route.query.draftId,
          lecturerIndex: route.query.lecturerIndex
        }
      });
    } else if (route.query.type === "edite") {
      router.replace({
        path: "/course/coursePeriodEdite",
        query: {
          type: route.query.type,
          periodId: route.query.periodId,
          courseId: route.query.courseId,
          fromPage: route.query.fromPage,
          create: createName,
          lecturerIndex: route.query.lecturerIndex
        }
      });
    }

    getTableList();
  } else {
    ElMessage.error(res.msg);
  }
  submitLoading.value = false;
};

const cancelForm = () => {
  const currentPath = router.currentRoute.value.path;
  let createName = "";
  if (!isEmpty(route.query.Lecturerindex)) {
    createName = "createTrip";
  } else {
    createName = "create";
  }
  if (currentPath.includes("periodCopy/create")) {
    // 从复制课期页面来的课期创建页面
    router.push({
      path: "/course/periodCopy/create",
      query: {
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        type: "copy"
      }
    });
  } else if (
    route.query.type === "create" ||
    route.query.type === "draft" ||
    route.query?.type === "createPeriod"
  ) {
    router.replace({
      path: "/course/courseCreate",
      query: {
        type: route.query.type,
        copyId: route.query.copyId,
        courseId: route.query.courseId,
        id: route.query.id,
        create: createName,
        draftId: route.query.draftId,
        lecturerIndex: route.query.lecturerIndex
      }
    });
  } else if (route.query.type === "edite") {
    router.replace({
      path: "/course/coursePeriodEdite",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        create: createName,
        lecturerIndex: route.query.lecturerIndex
      }
    });
  } else if (currentPath.includes("/account/teamCreate")) {
    // 从领队创建页面来的，返回领队创建页面
    router.push({
      path: "/account/teamManage"
    });
  } else if (currentPath.includes("/account/teacherCreate")) {
    // 从讲师创建页面来的，返回讲师创建页面
    router.push({
      path: "/account/teacherManage"
    });
  } else {
    // 默认返回账号管理页面
    router.push({
      path: "/account/accountManage"
    });
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_search">
      <el-form :model="form" :inline="true">
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            style="width: 310px"
            @change="timeChange"
          />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input
            v-model="form.name"
            placeholder="请输入"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="手机号 ">
          <el-input
            v-model="form.phone"
            placeholder="请输入"
            clearable
            style="width: 150px"
            @input="form.phone = form.phone.replace(/\D/g, '')"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="form.roleId"
            placeholder="请选择"
            clearable
            :style="{ width: '150px' }"
            @clear="form.roleId = 'all'"
          >
            <el-option
              v-for="role in roleLists"
              :key="role.id"
              :label="role.list_name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label=" ">
          <div class="flex">
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="setData">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="con_table">
      <el-table
        ref="tableRef"
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="账号ID">
          <template #default="scope">
            <el-text>
              {{ scope.row.id || "暂无" }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.name || "暂无" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="left">
          <template #default="scope">
            <div class="eye_style">
              {{
                scope.row.phone
                  ? scope.row.type_phone
                    ? decrypt(scope.row.phoneCt)
                    : scope.row.phone
                  : "-"
              }}
              <div
                v-if="scope.row.phone"
                class="eye"
                @click="eye_phone(scope.row.id, scope.row.phoneCt)"
              >
                <el-icon v-if="!scope.row.type_phone"><Hide /></el-icon>
                <el-icon v-else><View /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.account || "暂无" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="角色">
          <template #default="scope">
            <template v-if="scope.row.roles?.length">
              <div
                v-for="(item, index) in scope.row.roles"
                :key="index"
                style="display: inline-block"
              >
                {{ item.name || "--" }}
                <span v-if="index !== scope.row.roles.length - 1">、</span>
              </div>
            </template>
            <span v-else>暂无</span>
          </template>
        </el-table-column>

        <el-table-column width="200px" prop="createdAt" label="创建时间">
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "暂无"
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div v-if="selectRoleListId?.length" class="select_con">
        已选择{{ selectRoleListId?.length }}人：{{ selectRoleListName }}
      </div>
      <div class="btns">
        <el-button type="default" @click="cancelForm"> 取消 </el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ "确认选择" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  .con_search {
    padding: 20px 20px 2px 0;
    background-color: #fff;
    // margin-bottom: 20px;
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    background: #fff;
    padding: 0 20px 20px 0;
    box-sizing: border-box;
    height: 100%;
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }
  .eye_style {
    width: 120px;
    // align-items: center;
    // justify-content: center;
    .eye {
      float: right;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
  .btns {
    display: flex;
    justify-content: flex-end;
    margin-top: 5%;
  }
}
</style>

<script setup>
import { ref, defineEmits, computed } from "vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";

const props = defineProps({
  title: {
    type: String,
    default: "申请改期"
  },
  dialogFormVisible: {
    type: Boolean,
    default: false
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确定"
  }
});

const emit = defineEmits(["reset", "update:dialogFormVisible", "confirm"]);

const postponeOpenTime = ref("");
const reason = ref("");
const getListLoading = ref(false);
const selectedReasons = ref([]); // 用于跟踪已选择的理由

// 改期理由列表
const reasonList = ref([
  { id: 0, name: "天气或不可抗力因素" },
  { id: 1, name: "特殊活动或节假日" },
  { id: 2, name: "时间安排冲突" }
]);

// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value);
  }
});

// 选择理由事件
const selectReasonEvt = (item, index) => {
  const isSelected = selectedReasons.value.some(
    selected => selected.id === item.id
  );

  if (isSelected) {
    // 如果已选择，则移除
    const index = selectedReasons.value.findIndex(
      selected => selected.id === item.id
    );
    selectedReasons.value.splice(index, 1);
  } else {
    // 如果未选择，则添加
    selectedReasons.value.push(item);
  }

  // 更新文本框内容为所有已选择理由的连接
  reason.value = selectedReasons.value
    .map(reasonItem => reasonItem.name)
    .join("，");
};

// 处理理由输入
const handleReasonInput = value => {
  reason.value = value;
};

// 确认按钮点击事件
const btnOKClick = async () => {
  if (!postponeOpenTime.value) {
    ElMessage.warning("请选择改期时间");
    return;
  }

  if (!reason.value.trim()) {
    ElMessage.warning("请输入或选择改期理由");
    return;
  }

  if (getListLoading.value) {
    return;
  }

  getListLoading.value = true;

  try {
    // 触发确认事件，传递改期时间和理由
    emit("confirm", {
      postponeOpenTime: dayjs(postponeOpenTime.value).valueOf(),
      reason: reason.value
    });
  } catch (error) {
    console.error("申请改期失败：", error);
  } finally {
    getListLoading.value = false;
  }
};

// 取消按钮点击事件
const cancel = () => {
  selectedReasons.value = [];
  reason.value = "";
  postponeOpenTime.value = "";
  emit("reset");
};

// 弹窗关闭事件
const handleClose = () => {
  selectedReasons.value = [];
  reason.value = "";
  postponeOpenTime.value = "";
};
</script>

<template>
  <el-dialog
    v-model="localVisible"
    :title="title"
    width="515"
    @close="handleClose"
  >
    <div class="content">
      <div class="reschedule-form">
        <div class="form-item">
          <label class="form-label">改期时间：</label>
          <el-date-picker
            v-model="postponeOpenTime"
            type="date"
            placeholder="请选择改期时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <label class="form-label">改期理由：</label>
          <el-input
            v-model="reason"
            placeholder="请输入或选择改期理由"
            type="textarea"
            resize="none"
            maxlength="200"
            show-word-limit
            :rows="4"
            @input="handleReasonInput"
          />
          <div class="select-btn">
            <div
              v-for="(item, index) in reasonList"
              :key="index"
              :class="
                selectedReasons.some(selected => selected.id === item.id)
                  ? 'active-item'
                  : 'select-item'
              "
              @click="selectReasonEvt(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button :loading="getListLoading" type="primary" @click="btnOKClick">
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.reschedule-form {
  width: 100%;
  margin: 0 auto;

  .form-item {
    margin-bottom: 20px;

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    :deep(.el-textarea__inner) {
      min-height: 80px;
    }

    .select-btn {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .select-item {
        padding: 6px 12px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s;
        background-color: #fff;

        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background-color: #ecf5ff;
        }
      }

      .active-item {
        padding: 6px 12px;
        background-color: #409eff;
        color: #fff;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;
        border: 1px solid #409eff;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

import { http } from "@/utils/http";
/** 行程安排创建 */
export const itineraryCreate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/itinerary/create",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 行程安排编辑 */
export const itineraryUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/itinerary/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 行程安排编辑V2 */
export const itineraryUpdateV2 = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/itinerary/updateV2",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 行程安排删除 */
export const itineraryDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/itinerary/delete",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期Id查行程 */
export const findByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/itinerary/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查行程 */
export const findById = params => {
  return http.request(
    "get",
    "/organization/itinerary/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 创建和编辑费用（价格设置-费用退费说明） */
export const freeCreateOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/priceSetting/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 创建和编辑规格(价格设置) */
export const createOrUpdateSpecification = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/priceSetting/createOrUpdateSpecification",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期Id查费用 */
export const CoursePeriodIdByFree = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查规格 */
export const findSpecificationByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findFeeItemByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查规格 */
export const findSpecificationNameOptionByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findSpecificationNameOptionByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查价格明细 */
export const findFeeItemByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findFeeItemByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查规格 */
export const findSpecificationByCoursePeriodIdPrice = params => {
  return http.request(
    "get",
    "/organization/priceSetting/findSpecificationByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查课程报告 */
export const findCourseReportId = params => {
  return http.request(
    "get",
    "/organization/courseReport/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 创建和编辑课程报告 */
export const createOrUpdatecourseReport = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseReport/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期Id查课程介绍 */
export const introductionfindById = params => {
  return http.request(
    "get",
    "/organization/courseIntroduction/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查课程知识点 */
export const nowledgefindById = params => {
  return http.request(
    "get",
    "/organization/courseKnowledgePoint/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据id查询单条数据 */
export const findById2 = params => {
  return http.request(
    "get",
    "/organization/courseKnowledgePoint/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 课程周期创建 */
export const coursePeriodCreate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/create",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 课程周期编辑 */
/** 根据课期Id查材料说明 */
export const descriptionfindById = params => {
  return http.request(
    "get",
    "/organization/equipmentDescription/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查注意事项 */
export const precautionsfindById = params => {
  return http.request(
    "get",
    "/organization/precautions/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 创建和编辑课程介绍*/
export const createOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseIntroduction/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 创建知识点*/
export const nowledgeCreateOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseKnowledgePoint/create",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 编辑知识点 */
export const nowledgeCreateOrUpdate2 = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseKnowledgePoint/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 创建和编辑材料说明*/
export const descriptionCreateOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/equipmentDescription/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 创建和编辑注意事项*/
export const precautionsCreateOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/precautions/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期Id查用户评价 */
export const findCommentsAll = params => {
  return http.request(
    "get",
    "/organization/comments/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据Id查用户评价详情 */
export const findCommentsId = params => {
  return http.request(
    "get",
    "/organization/comments/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 用户评价机构回复 */
export const commentsReply = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/comments/reply",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期Id查上课跟踪 */
export const findClassTracking = params => {
  return http.request(
    "get",
    "/organization/classTracking/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查学生情况 */
export const findStudentSituation = params => {
  return http.request(
    "get",
    "/organization/studentSituation/findAllByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据学生实践感悟id查实践感悟批改分数内容 */
export const findByStudentAssignmentId = params => {
  return http.request(
    "get",
    "/organization/assignmentDesign/findByStudentAssignmentId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 实践感悟批改（批改分数） */
export const gradingWork = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/assignmentDesign/grading",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 查询学生实践感悟 */
export const findStudentAssignment = params => {
  return http.request(
    "get",
    "/organization/assignmentDesign/findStudentAssignment",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 实践感悟设计（实践感悟）创建编辑 */
export const assignmentDesign = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/assignmentDesign/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据课期id查询实践感悟设计（实践感悟） */
export const findAssignmentDesign = params => {
  return http.request(
    "get",
    "/organization/assignmentDesign/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据订单id查询订单详情 */
export const orderDetail = params => {
  return http.request(
    "get",
    "/organization/orders/getOrderDetails",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 退子订单 */
export const confirmRefundSub = data => {
  return http.request(
    "post",
    "/organization/orders/confirmRefundSub",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/**课期复制*/
export const coursePeriodCopy = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/copy",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 课程多级分类查询 */
export const findAllCourseType = params => {
  return http.request(
    "get",
    "/organization/courseType/findAllCourseType",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/**取消审核（撤销申请）*/
export const periodcancelReview = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/cancelReview",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**查询课期 */
export const coursePeriodAll = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/findAllV2",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期Id查询用户协议 */
export const userAgreementId = params => {
  return http.request(
    "get",
    "/organization/userAgreement/findByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/**用户协议编辑*/
export const userAgreementUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/userAgreement/createOrUpdate",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**价格设置（包括规格及费用说明等）编辑*/
export const updatePriceSetting = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/priceSetting/updateCoursePeriodFee",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 课程知识点学段查询 */
export const findCourseKnowledgePointByGrade = params => {
  return http.request(
    "get",
    "/organization/dataStage/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 课程知识点学科查询 */
export const findCourseKnowledgePointBySubject = params => {
  return http.request(
    "get",
    "/organization/subject/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

/** 课程教材版本查询 */
export const findCourseMaterialVersion = params => {
  return http.request(
    "get",
    "/organization/dataTextbookVersion/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 课程目录查询 */
// export const findCourseCoreKnowledgePoint = params => {
//   return http.request(
//     "get",
//     "/organization/dataKnowledge/findAll",
//     { params },
//     { isNeedEncrypt: true, isNeedToken: true }
//   );
// };

/**课程知识点 */
export const findCourseKnowledgePoint = params => {
  return http.request(
    "get",
    "/organization/dataKnowledge/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

/** 课程目录查询  */
export const findCourseDataCourse = params => {
  return http.request(
    "get",
    "/organization/dataCatalog/findAll",
    // '/organization/dataCourse/findAll',
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

/** 能力提升  */
export const findCourseDataCourseBySubject = params => {
  return http.request(
    "get",
    "/common/dict/findModelChildrenByParentId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

// 创建课程知识点
export const saveDraftCourseKnowledge = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseKnowledgePoint/create",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 课程知识点联级查询
// export const findCourseKnowledgePointByParentId = params => {
//   return http.request(
//     "get",
//     "/common/dict/findByNodeId",
//     { params },
//     { isNeedEncrypt: true, isNeedToken: true }
//   );
// }
// 删除知识点
export const deleteCourseKnowledgePoint2 = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/courseKnowledgePoint/delete",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

<script setup>
import { ref, onMounted } from "vue";
import { coursePeriodFind } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  // 查看使用范围是否显示
  showUseScope: {
    type: Boolean,
    default: true
  }
});
const router = useRouter();
const form = ref({
  name: "",
  couponType: "",
  enabled: "",
  amount: "",
  couponScope: "",
  issUedTime: "",
  useTime: "",
  remarks: "",
  createdAt: ""
});
const tableHeader = ref([
  {
    id: "1",
    label: "优惠券名称：",
    value: "",
    prop: "name",
    width: "107px"
  },
  {
    id: "2",
    label: "优惠券类型：",
    value: "",
    prop: "couponType",
    width: "107px"
  },
  {
    id: "3",
    label: "状态：",
    value: "",
    prop: "enabled",
    width: "107px"
  },
  {
    id: "4",
    label: "优惠门槛与金额：",
    value: "",
    prop: "amount",
    width: "107px"
  },
  {
    id: "5",
    label: "适用范围：",
    value: "",
    prop: "couponScope",
    width: "107px"
  },

  {
    id: "6",
    label: "发放时间：",
    value: "",
    prop: "issUedTime",
    width: "107px"
  },

  {
    id: "7",
    label: "使用时间：",
    value: "",
    prop: "useTime",
    width: "107px"
  },
  {
    id: "8",
    label: "创建时间：",
    value: "",
    prop: "createdAt",
    width: "107px"
  },
  {
    id: "9",
    label: "备注：",
    value: "",
    prop: "remarks",
    width: "107px"
  }
]);
// 查询详情
const getData = async () => {
  let [err, res] = await requestTo(coursePeriodFind({ id: props.id || 0 }));
  if (res) {
    // console.log("🐬-----res1111--333---", res);
    coursePeriodInfo.value = res || {};
    tableHeader.value[0].value = res.name || "--";
  } else {
    console.log("🐳-----err-----", err);
  }
};
// 查看使用范围
const showUseScopeDetail = () => {
  router.push({
    path: "/course/coupon/detail/scopeDetails"
  });
};
onMounted(() => {});
</script>

<template>
  <div>
    <div v-if="showUseScope" class="margin-top-btn">
      <el-button type="primary" @click="showUseScopeDetail">
查看使用范围
</el-button>
    </div>
    <el-descriptions
      class="margin-top"
      title=""
      :column="2"
      border
      style="width: 100%"
      :label-width="'200px'"
    >
      <template v-for="(item, index) in tableHeader" :key="index">
        <el-descriptions-item
          label-class-name="my-label"
          label-align="right"
          :span="item.span || 1"
        >
          <template #label>
            <div class="cell-item">
              {{ item.label }}
            </div>
          </template>
          <div v-if="item.type !== 'time'">
            <div>{{ form[item.prop] || "--" }}</div>
          </div>
          <div v-else>
            {{
              item.prop === "createdAt" || item.prop === "establishmentTime"
                ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                : form[item.prop] || "--"
            }}
          </div>
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>

<style lang="scss" scoped>
:deep(.my-label) {
  background: #fff !important;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  font-weight: normal;
}

.margin-top-btn {
  display: flex;
  justify-content: right;
}
</style>

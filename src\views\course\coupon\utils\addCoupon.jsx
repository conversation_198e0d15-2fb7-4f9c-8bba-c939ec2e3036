import { ref, reactive, computed, nextTick, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { debounce } from "@iceywu/utils";
import { PlusSearch, PlusTable } from "plus-pro-components";

export default function useAddCoupon() {
  const router = useRouter();
  const route = useRoute();

  // 表单数据
  const form = reactive({
    name: "", // 优惠券名称
    feeType: "", // 优惠券费用类型
    couponDiscountType: "", // 优惠券使用类型
    totalIssue: "", // 发放数量
    issUedTime: [], // 发放时间
    useTime: [], // 使用时间
    remarks: "", // 备注
    enabled: "", // 状态
    coursePeriodIds: "", // 可用课程/课期
    discountAmount: "", // 减金额
    conditionAmount: "", // 满金额
    isDistributeLimit: "", // 发放是否有限制
    isUseLimit: "", // 使用是否有限制
    noLimitNumber: "", // 每人限领数量
    couponScope: "", // 优惠券使用范围
    usedCourse: 1, // 使用范围 - 默认设置为1(通用)
    maxAmount: "", // 满金额
    minAmount: "" // 减金额
  });

  // 添加 form 的监听器用于调试
  watch(
    () => form.usedCourse,
    (newVal, oldVal) => {
      console.log("🍪-----form.usedCourse changed-----", { oldVal, newVal });
    },
    { immediate: true }
  );

  const formRef = ref(null);
  const richFlag = ref(false);

  // 表单文件数据
  const formFile = ref({
    institutionLicense: [],
    qualificationDocuments: [],
    logo: [],
    video: [],
    environment: []
  });

  // 基本信息表单配置
  const formData = ref([
    {
      label: "优惠券名称",
      type: "input",
      prop: "name",
      check: true,
      width: "400px",
      placeholder: "请输入优惠券名称"
    },
    {
      label: "优惠券费用类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "discountFreeType",
      placeholder: "请选择优惠券费用类型",
      options: [
        { name: "课时", value: 1 },
        { name: "材料", value: 2 }
      ]
    },
    {
      label: "优惠券使类型",
      type: "radioInput",
      check: true,
      prop: "couponType",
      maxLength: 30,
      width: "400px",
      placeholder: "请选择优惠券使类型"
    },
    {
      label: "发放数量",
      type: "input",
      typeInput: "number",
      check: true,
      maxLength: 11,
      width: "400px",
      prop: "quantityIssued",
      placeholder: "请输入发放数量"
    },
    {
      label: "发放时间",
      type: "date",
      check: true,
      prop: "issUedTime",
      maxLength: 20,
      width: "400px",
      placeholder: "请选择发放时间"
    },
    {
      label: "使用时间",
      type: "dateRadio",
      check: true,
      prop: "useTime",
      maxLength: 20,
      placeholder: "请选择使用时间",
      options: [
        { name: "不限", value: 1 },
        { name: "有限", value: 2 }
      ]
    },
    {
      label: "备注",
      type: "textarea",
      prop: "remarks",
      width: "400px",
      maxLength: 200,
      placeholder: "请输入备注"
    },
    {
      label: "状态",
      type: "radio",
      check: true,
      prop: "status",
      maxLength: 50,
      placeholder: "请选择状态",
      options: [
        { name: "启用", value: 1 },
        { name: "停用", value: 2 }
      ]
    }
  ]);

  // 使用范围表单配置
  const formData2 = ref([
    {
      label: "使用范围",
      type: "radio",
      check: true,
      maxLength: 50,
      width: "400px",
      prop: "usedCourse",
      placeholder: "请选择使用范围",
      options: [
        { name: "通用", value: 1 },
        { name: "指定", value: 2 }
      ]
    }
  ]);

  // 优惠券使用范围相关数据
  const scopeForm = reactive({
    courseName: "",
    coursePeriodName: "",
    coursePeriodStatus: "不限"
  });

  // 搜索列配置
  const searchColumns = ref([
    {
      label: "课程名称",
      prop: "courseName",
      type: "input",
      placeholder: "请输入课程名称",
      span: 8
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      type: "input",
      placeholder: "请输入课期名称",
      span: 8
    },
    {
      label: "课期状态",
      prop: "coursePeriodStatus",
      type: "select",
      span: 8,
      options: [
        { label: "不限", value: "不限" },
        { label: "已上架", value: "已上架" },
        { label: "未上架", value: "未上架" }
      ]
    }
  ]);

  // 表格列配置
  const tableColumns = ref([
    {
      type: "selection",
      width: 55,
      align: "center"
    },
    {
      label: "期号",
      prop: "issueNumber",
      width: 80,
      align: "center"
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      minWidth: 200
    },
    {
      label: "开课时间",
      prop: "startDate",
      width: 120,
      align: "center"
    },
    {
      label: "购买类型",
      prop: "purchaseType",
      width: 100,
      align: "center"
    },
    {
      label: "课期状态",
      prop: "coursePeriodStatus",
      width: 100,
      align: "center",
      formatter: ({ coursePeriodStatus }) => {
        if (coursePeriodStatus === "已上架") {
          return '<span style="color: #67c23a;">已上架</span>';
        } else if (coursePeriodStatus === "未上架") {
          return '<span style="color: #909399;">未上架</span>';
        }
        return coursePeriodStatus;
      }
    }
  ]);

  // 假数据 - 课程列表
  const tableData = ref([
    {
      id: 1,
      issueNumber: 1,
      coursePeriodName: "新用户专享",
      startDate: "2025-04-11",
      purchaseType: "普通单",
      coursePeriodStatus: "已上架"
    },
    {
      id: 2,
      issueNumber: 2,
      coursePeriodName: "老客户回归",
      startDate: "2025-04-11",
      purchaseType: "普通单",
      coursePeriodStatus: "已上架"
    },
    {
      id: 3,
      issueNumber: 3,
      coursePeriodName: "轻松一夏",
      startDate: "2025-04-11",
      purchaseType: "普通单",
      coursePeriodStatus: "已上架"
    },
    {
      id: 4,
      issueNumber: 4,
      coursePeriodName: "快乐一起营",
      startDate: "2025-04-11",
      purchaseType: "普通单",
      coursePeriodStatus: "未上架"
    },
    {
      id: 5,
      issueNumber: 5,
      coursePeriodName: "机构5",
      startDate: "2025-04-11",
      purchaseType: "普通单",
      coursePeriodStatus: "未上架"
    }
  ]);

  // 确保表格数据的稳定性
  const stableTableData = computed(() => {
    return tableData.value;
  });

  // 分页配置
  const pagination = reactive({
    page: 1,
    size: 5,
    total: 25
  });

  // 加载状态
  const loading = ref(false);
  const getListLoading = ref(false);

  // 选中的行
  const selectedRows = ref([]);

  // 计算属性：是否显示使用范围选择区域
  const showScopeSelection = computed(() => {
    console.log("🍪-----form.usedCourse-----", form.usedCourse);
    console.log("🍪-----showScopeSelection-----", form.usedCourse === 2);
    return form.usedCourse === 2; // 当选择"指定"时显示
  });

  // 搜索方法
  const onSearch = () => {
    if (loading.value) return; // 防止重复搜索

    loading.value = true;
    setTimeout(() => {
      console.log("搜索条件:", scopeForm);
      // 使用 nextTick 确保 DOM 更新完成后再设置 loading 状态
      nextTick(() => {
        loading.value = false;
      });
    }, 500);
  };

  // 重置方法
  const onReset = () => {
    Object.keys(scopeForm).forEach(key => {
      scopeForm[key] = key === "coursePeriodStatus" ? "不限" : "";
    });
    onSearch();
  };

  // 分页改变
  const onPageChange = page => {
    if (pagination.page === page) return; // 防止重复更新

    pagination.page = page;
    onSearch();
  };

  // 每页条数改变
  const onSizeChange = size => {
    if (pagination.size === size) return; // 防止重复更新

    pagination.size = size;
    pagination.page = 1;
    onSearch();
  };

  // 选择改变
  const onSelectionChange = selection => {
    // 使用 nextTick 避免递归更新
    nextTick(() => {
      // 防止递归更新，比较数组内容而不是引用
      const isSameSelection =
        JSON.stringify(selectedRows.value) === JSON.stringify(selection);
      if (!isSameSelection) {
        selectedRows.value = [...selection]; // 使用展开运算符创建新数组
        console.log("选中的行:", selection);
      }
    });
  };

  // 监听使用范围变化
  const onScopeChange = value => {
    console.log("🍪-----onScopeChange value-----", value);
    console.log("🍪-----form.usedCourse before-----", form.usedCourse);

    // 使用 nextTick 避免在同一个事件循环中更新
    nextTick(() => {
      if (value === 1) {
        // 选择"通用"时，清空选中的课程
        selectedRows.value = [];
      }
      console.log("使用范围变化:", value);
      console.log("🍪-----form.usedCourse after-----", form.usedCourse);
    });
  };

  // 返回上一页
  const reset = () => {
    router.go(-1);
  };

  // 校验规则
  const rules = ref({
    name: [{ required: true, message: "请输入优惠券名称", trigger: "blur" }],
    discountFreeType: [
      { required: true, message: "请选择优惠券费用类型", trigger: "blur" }
    ]
  });

  // 提交处理
  const onSubmit = async () => {
    if (getListLoading.value) return;

    // 表单验证
    formRef.value.validate(valid => {
      if (!valid) {
        ElMessage.error("请完善表单信息");
        return;
      }

      // 验证使用范围
      if (form.usedCourse === 2 && selectedRows.value.length === 0) {
        ElMessage.warning("请至少选择一个课程");
        return;
      }

      // 构建提交数据
      let paramsData = { ...form };

      // 如果选择"指定"使用范围，添加选中的课程信息
      if (form.usedCourse === 2) {
        paramsData.selectedCourses = selectedRows.value.map(row => ({
          id: row.id,
          issueNumber: row.issueNumber,
          coursePeriodName: row.coursePeriodName,
          startDate: row.startDate,
          purchaseType: row.purchaseType,
          coursePeriodStatus: row.coursePeriodStatus
        }));
      }

      console.log("提交数据:", paramsData);

      // 这里可以调用实际的API接口
      // const result = await createCoupon(paramsData);

      ElMessage.success("优惠券创建成功！");
      router.go(-1);
    });
  };

  // 提交表单
  const submitForm = debounce(
    () => {
      formRef.value.validate(valid => {
        if (valid) {
          console.log("🍪-----valid-----", valid);
          onSubmit();
        } else {
          console.log("表单校验失败");
        }
      });
    },
    1000,
    { immediate: true }
  );
  // 其他数据
  const newData = ref();
  const oldData = ref();

  // 测试方法
  const testScopeChange = () => {
    console.log("🍪-----测试点击指定-----");
    form.usedCourse = 2;
    console.log("🍪-----form.usedCourse 设置为 2-----", form.usedCourse);
    console.log("🍪-----showScopeSelection 值-----", showScopeSelection.value);
  };

  return {
    // 路由相关
    router,
    route,

    // 表单相关
    form,
    formRef,
    formData,
    formData2,
    rules,

    // 使用范围相关
    scopeForm,
    searchColumns,
    tableColumns,
    tableData,
    stableTableData,
    showScopeSelection,

    // 分页相关
    pagination,

    // 状态相关
    loading,
    getListLoading,
    selectedRows,
    richFlag,
    formFile,

    // 方法
    onSearch,
    onReset,
    onPageChange,
    onSizeChange,
    onSelectionChange,
    onScopeChange,
    reset,
    submitForm,
    onSubmit,

    // 测试方法
    testScopeChange,

    // 其他数据
    newData,
    oldData
  };
}

<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits, computed, nextTick } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import baseUrl, { codeInfo } from "@/utils/http/base.js";
import { isEmpty } from "@iceywu/utils";
import { courseFindId } from "@/api/course.js";
const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  },
  dialogFormVisible: {
    type: Boolean
  },
  logOut: {
    type: Boolean,
    default: true
  },
  operateLogType: {
    type: String,
    default: "COMPLEX_MANAGEMENT"
  },
  textLeftBtn: {
    type: String,
    default: ""
  },
  textRightBtn: {
    type: String,
    default: ""
  },
  operateType: {
    type: String,
    default: "重置了密码"
  },
  showContent: {
    type: String,
    default: "resetPassword"
  },
  marginLeft: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["reset", "update:dialogFormVisible", "updateData"]);
const router = useRouter();
const url = ref(
  "https://research-learning-file.frphttps.boran-tech.com/research-learning/dev/1958410134403481600.png"
);

onMounted(() => {
  getTableList();
});
// const dialogFormVisible = ref(false);
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: props.id,
    password: encryption(newPassword.value)
  };

  const operateLog = {
    operateLogType: props.operateLogType,
    operateType: props.operateType || "重置了密码"
  };
  // console.log("🐳paramsData------------------------------>", paramsData);
  // return
  try {
    const { code, data, msg } = await props.api(paramsData, operateLog);
    if (code === 200) {
      if (props.showContent === "notSale") {
        ElMessage({
          message: "课期下架申请提交，请等待平台审核",
          type: "success"
        });
        selectedReasons.value = [];
        customReason.value = "";
        emit("updateData");
      }
    } else {
      if (props.showContent === "notSale") {
        ElMessage.error(`下架申请提交失败，${msg}`);
      }
      getListLoading.value = false;
    }
  } catch (error) {
    if (props.showContent === "notSale") {
      ElMessage.error(`下架申请提交失败，${msg}`);
    }
  }
  getListLoading.value = false;
  emit("reset");
};
const cancel = () => {
  emit("reset");
};
const getDataLoading = ref(false);
// 查询
const getTableList = async data => {
  if (getDataLoading.value) {
    return;
  }
  getDataLoading.value = true;
  let paramsData = {
    id: props.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    url.value = result?.data?.couponQrcode;
  } else {
    ElMessage.error(err);
  }
  getDataLoading.value = false;
};

// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value); // 通知父组件更新
  }
});
const handleClose = () => {};
</script>

<template>
  <!-- <div class="popup"> -->
  <el-dialog
    v-model="localVisible"
    :title="title || '重置密码确定'"
    width="570"
    @close="handleClose"
  >
    <div class="describe">
      <div class="qrCode">
        <!-- <el-image :src="qrCodeData" style="height: 100%" /> -->
        <el-image :src="url" style="height: 100%" :hide-on-click-modal="true" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="textLeftBtn"
          :style="{ 'margin-right': marginLeft }"
          @click="cancel"
        >
          {{ textLeftBtn }}
        </el-button>
        <el-button
          v-if="textRightBtn"
          :loading="getListLoading"
          :type="
            textRightBtn === '确认'
              ? 'primary'
              : textRightBtn === '确认开启'
                ? 'primary'
                : 'danger'
          "
          @click="btnOKClick"
        >
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  :nth-child(2) {
    margin: 20px 0 20px 0px;
  }
}
.describe {
  width: 80%;
  height: 130px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.qrCode {
  width: 120px;
  height: 120px;
  background: #eee;
  margin-bottom: 20px;
  margin: 0 auto;
}
</style>

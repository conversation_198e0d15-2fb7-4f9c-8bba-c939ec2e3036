/**
 * 接口域名的管理
 * @param {String} apiServer [api服务器]
 */
const baseList = [
  // 开发环境
  {
    // apiServer: "http://10.0.30.109:9000", //qi
    apiServer: "http://10.0.30.183:9000", //qiu
    // apiServer: "http://10.0.30.117:9000", //ren
    // apiServer: "http://k8s-dev.boran-tech.cn:31392", //api服务器
    filePreviewServer: "https://api.ebag.readboy.com/wps", //文件预览
    imageProxy: "http://k8s-dev.boran-tech.cn:32185", //图片代理
    aiQuestion: "http://10.0.30.183:9004" //AI工具,
  },
  // 本地K8S测试环境
  {
    apiServer: "http://k8s-dev.boran-tech.cn:31392", //api服务器
    filePreviewServer: "https://api.ebag.readboy.com/wps", //文件预览
    imageProxy: "http://k8s-dev.boran-tech.cn:32185", //图片代理
    aiQuestion: "http://k8s-dev.boran-tech.cn:30969" // AI工具,
  },
  // 生产环境
  {
    apiServer: "https://gs-svc.gostaredu.com", //api服务器
    filePreviewServer: "https://api.ebag.readboy.com/wps", //文件预览
    imageProxy: "https://gs-svc.gostaredu.com/img-cmp/", //图片代理
    aiQuestion: "https://ai-curriculum.gostaredu.com/api-server" //AI工具
  }
];
/**
 * 网页域名的管理
 * @param {String} aiServer [ai工具网页地址]
 */
const webList = [
  // 开发环境
  {
    aiServer: "http://k8s-dev.boran-tech.cn:32029/#/index"
  },
  // 本地K8S测试环境
  {
    aiServer: "http://k8s-dev.boran-tech.cn:32029/#/index"
  },
  // 生产环境
  {
    aiServer: "https://ai-curriculum.gostaredu.com/#/index"
  }
];

/**
 * //微信登录不同环境code码
 * @param {String} code [code码]
 */
const codeList = [
  // 本地开发环境 0
  {
    url: "https://gs-svc.gostaredu.com",
    code: 0,
    app_id: "wx4f152d3ccf5af3ed",
    qrParams: {
      checkPath: false,
      envVersion: "develop",
      page: "pages/home/<USER>"
    }
  },
  // 本地K8S环境 1
  {
    url: "https://gs-svc.gostaredu.com",
    code: 0,
    app_id: "wx4f152d3ccf5af3ed",
    qrParams: {
      checkPath: false,
      envVersion: "trial",
      page: "pages/home/<USER>"
    }
  },
  //正式环境 2
  {
    url: "https://gs-svc.gostaredu.com",
    code: 2,
    app_id: "wx4f152d3ccf5af3ed",
    qrParams: {
      checkPath: false,
      envVersion: "trial",
      page: "pages/home/<USER>"
    }
  },
  //中山环境 3
  {
    url: "",
    code: 4,
    app_id: "wxc17cae3694521c55",
    qrParams: {
      checkPath: false,
      envVersion: "release",
      page: "zhongshan-special/home/<USER>"
    }
  }
];

const ServerNumber = import.meta.env.VITE_APP_SERVER_ID
  ? import.meta.env.VITE_APP_SERVER_ID
  : 0;
// const ServerNumber = 1;
const baseUrl = baseList[ServerNumber];
export const webUrl = webList[ServerNumber];
export const codeInfo = codeList[ServerNumber];

export default baseUrl;

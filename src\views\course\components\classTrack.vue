<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findClassTracking } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
const router = useRouter();
const route = useRoute();

const trackList = ref([]);
const tableData = ref([]);
// 课期上课跟踪查询
const findCoursePeriodList = async type => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  let [err, res] = await requestTo(findClassTracking(params));
  if (res) {
    // console.log("🌳-----res-----", res);
    let list = res.map(it => {
      if (it.files && it.files.length) {
        it.files = it.files.map(item => {
          item.url = item.uploadFile.url;
          const fileName = item.uploadFile.fileName;
          const extension = fileName.split(".").pop().toLowerCase();
          // 判断是否为视频文件
          const videoExtensions = [
            "mp4",
            "avi",
            "mkv",
            "mov",
            "wmv",
            "flv",
            "webm",
            "m4v"
          ];
          if (videoExtensions.includes(extension)) {
            item.type = "video";
          } else {
            item.type = "image";
          }

          return item;
        });
      }
      return it;
    });
    trackList.value = list;
  } else {
    console.log("🌵-----err-----", err);
  }
};
onMounted(() => {
  findCoursePeriodList();
});
</script>

<template>
  <div class="class-track">
    <div v-if="trackList?.length" class="content">
      <el-timeline>
        <el-timeline-item
          v-for="item in trackList"
          :key="item.id"
          :timestamp="item.timestamp"
        >
          <div class="timeLine_title">
            <span class="m-w">{{
              dayjs(item.timePoint).format("YYYY-MM-DD HH:mm:ss") || ""
            }}</span>
          </div>
          <div class="item_content">
            <div class="content">{{ item.content || "--" }}</div>
            <div v-if="item.files && item.files.length" class="img-box">
              <div v-for="(it, index) in item.files" :key="index">
                <img
                  v-if="it.type === 'image'"
                  v-preview="{
                    url: it?.uploadFile?.url,
                    type: 'image',
                    name: it?.uploadFile?.fileName
                  }"
                  :src="it?.uploadFile?.url"
                  alt=""
                  class="img"
                >
                <video
                  v-else
                  :src="it?.uploadFile?.url"
                  controls
                  class="img"
                />
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <el-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.class-track {
  height: 100%;
  width: 100%;
  position: relative;

  .content {
    width: 100%;
    height: 94%;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    -ms-overflow-style: none;
    scrollbar-width: none;
    .timeLine_title {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .item_content {
      margin-top: 20px;
      width: 100%;
      padding-left: 30px;
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
      .img-box {
        display: flex;
        width: 100%;
        .img {
          width: 250px;
          height: 140px;
          margin-right: 15px;
          object-fit: cover;
        }
      }
    }
  }
  // .content::-webkit-scrollbar {
  //   display: none;
  // }
  .content::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景 */
  }

  .content::-webkit-scrollbar-thumb {
    background: #afafaf; /* 滚动条滑块颜色 */
  }

  .content::-webkit-scrollbar-thumb:hover {
    background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
  }
  .m-w {
    min-width: fit-content;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>

<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  findByOrganization,
  deleteOrganizationTeacher
} from "@/api/teachers/institutionalFaculty.js";
import { teacherDataFindAll } from "@/api/teachers/teacherResourcePool.js";
import { teacherInvitationFindAll } from "@/api/teachers/invitationManagement.js";
import { decrypt } from "@/utils/SM4.js";
import { View, Hide, WarningFilled, Loading } from "@element-plus/icons-vue";
import { downloadFile } from "@iceywu/utils";
import {
  EDUCATION_LEVEL,
  RESIDENT_STATUS,
  INVITE_TYPE,
  INVITE_STATE
} from "@/utils/enum.js";
import {
  columns,
  failureColumns,
  discountType,
  discountStatus,
  scopeOptions
} from "@/views/course/coupon/utils/options.js";
import {
  getCouponFindAll,
  distributeCoupon,
  enabledCoupon
} from "@/api/coupon.js";
import DistributeDialog from "@/components/Base/orderDialog.vue";
import AddressDialog from "./components/addressDialog.vue";
defineOptions({
  name: "CouponIndex"
});

const router = useRouter();
const route = useRoute();

const loadingTable = ref(false);
const educationOptions = ref([]); //学历选项
const dialogConfirmVisible = ref(false); //确认派发弹窗
const dialogFormVisible = ref(false); //手动派发弹窗
const dialogStopVisible = ref(false); //停用弹窗
const dialogStartVisible = ref(false); //启用弹窗
const dialogAddressVisible = ref(false); //领取地址弹窗
const dataInfo = ref({}); //优惠券数据
const dataList = ref([]);
const tabTitle = ref([
  { name: "有效", value: 0 },
  { name: "已失效", value: 1 }
]);
const activeName = ref(0);
const form = reactive({
  name: "",
  feeType: "",
  couponScope: "",
  enabled: "",
  distributionEndTime: "", //领取结束时间
  distributionStartTime: "", //领取开始时间
  endTime: "", //使用结束时间
  startTime: "" //使用开始时间
});

const pagination = {
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true,
  pageSizes: [15, 30, 50, 100]
};

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc"
});
// 列表 api
const onSearch = async val => {
  const paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    timeStamp: dayjs().valueOf(),
    valid: activeName.value === 0 ? "valid" : "invalid"
  };
  for (const paramsDataKey in form) {
    let isArray = Array.isArray(form[paramsDataKey]);
    if (isArray) {
      if (form[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form[paramsDataKey];
      }
    } else {
      if (paramsDataKey === "freeze") {
        if (form[paramsDataKey] !== "all") {
          paramsData[paramsDataKey] = form[paramsDataKey];
        }
      } else if (form[paramsDataKey]) {
        paramsData[paramsDataKey] = form[paramsDataKey];
      }
    }
  }
  const [err, res] = await requestTo(getCouponFindAll(paramsData));
  if (res) {
    console.log("🍪res------------------------------>", res);
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
    console.error(err);
  }
};

// 搜索
const searchData = () => {
  params.value.page = 1;
  onSearch();
};

// 重置
const setData = () => {
  params.value.page = 1;
  onSearch();
};

// 每页多少条
const handleSizeChange = val => {
  pagination.pageSize = val;
  params.value.size = val;
  onSearch();
};
// 前往页数
const handleCurrentChange = val => {
  pagination.currentPage = val;
  params.value.page = val;
  onSearch();
};

// 切换手机号码显示状态t
const eye_phone = (id, phoneCt) => {
  const item = dataList.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
const closeDistributeCoupon = val => {
  dataInfo.value.phone = val;
  dialogFormVisible.value = false;
  dialogConfirmVisible.value = true;
};
// 弹框显示
const dialogEvt = (row, type) => {
  if (type === "distributeCoupon") {
    dataInfo.value = row;
    console.log(
      "🌈dataInfo.value------------------------------>",
      dataInfo.value
    );
    dialogFormVisible.value = true;
  }
};
const handleClick = val => {
  activeName.value = val?.props?.name;
  params.value.page = 1;
  onSearch();
};
// 选择领取时间
const timeGetChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = value[1];
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};
// 选择使用时间
const timeUseChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = value[1];
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};
// 更新数据
const update = () => {
  onSearch();
};
onActivated(() => {
  onSearch();
});
onMounted(async () => {
  onSearch();
});
</script>

<template>
  <div>
    <div class="common">
      <div class="search">
        <div class="search-form">
          <el-form :model="form" class="demo-form-inline" :inline="true">
            <el-form-item label="优惠券名">
              <el-input
                v-model.trim="form.name"
                placeholder="优惠券名"
                clearable
                style="width: 210px"
              />
            </el-form-item>
            <el-form-item label="优惠券费用类型">
              <el-select
                v-model="form.couponDiscountType"
                style="width: 120px"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in discountType"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="使用范围">
              <el-select
                v-model="form.couponScope"
                style="width: 120px"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in scopeOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="activeName === 0" label="状态">
              <el-select
                v-model="form.enabled"
                style="width: 120px"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in discountStatus"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="领取时间">
              <el-date-picker
                v-model="value1"
                type="daterange"
                start-placeholder="请选择开始时间"
                end-placeholder="请选择结束时间"
                style="margin-right: 30px"
                @change="timeGetChange"
                @clear="clearEvt('time')"
              />
            </el-form-item>
            <el-form-item label="使用时间">
              <el-date-picker
                v-model="value2"
                type="daterange"
                start-placeholder="请选择开始时间"
                end-placeholder="请选择结束时间"
                @change="timeUseChange"
              />
            </el-form-item>
            <el-form-item label=" ">
              <div class="flex">
                <el-button type="primary" @click="searchData">搜索</el-button>
                <el-button @click="setData">重置</el-button>
                <el-button
                  type="primary"
                  @click="
                    router.push({
                      path: '/course/coupon/add',
                      query: { type: 'add' }
                    })
                  "
                >
                  新建优惠券
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <div class="common">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabTitle"
          :key="index"
          :label="item.name"
          :name="index"
        />
      </el-tabs>
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="activeName === 0 ? columns : failureColumns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #enabled="{ row }">
            <div
              :style="{
                color:
                  row.enabled === true
                    ? 'var(--el-color-success)'
                    : 'var(--el-color-danger)'
              }"
            >
              {{ row.enabled === true ? "启用" : "停用" }}
            </div>
          </template>
          <template #operation="{ row }">
            <el-button
              type="primary"
              link
              @click="
                router.push({
                  path: '/course/coupon/detail',
                  query: { id: row.id }
                })
              "
            >
              详情
            </el-button>
            <el-button
              v-if="activeName === 0"
              type="primary"
              link
              @click="dialogEvt(row, 'distributeCoupon')"
            >
              手动派发
            </el-button>
            <el-button
              v-if="activeName === 0 && row.status === 'INVITING'"
              type="default"
              link
              @click="
                router.push({
                  path: '/invitation/details',
                  query: { type: 'invite', id: row.teacherId }
                })
              "
            >
              券已领完
            </el-button>
            <el-button
              v-if="activeName === 0 && row.enabled === false"
              type="primary"
              link
              @click="dialogStartVisible = true"
            >
              启用
            </el-button>
            <el-button
              v-if="activeName === 0 && row.enabled === true"
              type="danger"
              link
              @click="dialogStopVisible = true"
            >
              停用
            </el-button>
            <el-button
              v-if="activeName === 0"
              type="primary"
              link
              @click="dialogAddressVisible = true"
            >
              领取地址
            </el-button>
            <!-- <el-button
              v-if="activeName === 0"
              type="primary"
              link
              @click="
                router.push({
                  path: '/course/current/detail',
                  query: { periodId: row.coursePeriodId }
                })
              "
            >
              更多
            </el-button> -->
            <el-popover v-if="activeName === 0" placement="bottom" :width="400">
              <template #reference>
                <el-button type="primary" link>更多</el-button>
              </template>
              <el-button
                type="primary"
                link
                @click="
                  router.push({
                    path: '/course/coupon/add',
                    query: { type: 'edit' }
                  })
                "
              >
                复制
              </el-button>
              <el-button
                type="primary"
                link
                @click="
                  router.push({
                    path: '/course/coupon/scopeEdit',
                    query: { periodId: row.coursePeriodId }
                  })
                "
              >
                修改使用范围
              </el-button>
            </el-popover>
            <el-button
              v-if="activeName !== 0"
              type="primary"
              link
              @click="
                router.push({
                  path: '/course/coupon/add',
                  query: { type: 'edit' }
                })
              "
            >
              复制
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
    <DistributeDialog
      v-model:dialogFormVisible="dialogFormVisible"
      :couponTarget="'distributeCoupon'"
      :logOut="false"
      :showContent="'coupon'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'手动派发'"
      :marginLeft="'10px'"
      :dataInfo="dataInfo"
      :width="450"
      @reset="closeDistributeCoupon"
    />
    <DistributeDialog
      v-model:dialogFormVisible="dialogConfirmVisible"
      :logOut="false"
      :couponTarget="'confirmCoupon'"
      :api="distributeCoupon"
      :operateLogType="'COUPON_MANAGEMENT'"
      :operateType="`手动派发了“${dataInfo?.name}”的优惠券给“${dataInfo?.phone}用户”`"
      :showContent="'coupon'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'确认派发'"
      :marginLeft="'10px'"
      :dataInfo="dataInfo"
      :width="450"
      @reset="dialogConfirmVisible = false"
      @update-data="update"
    />
    <DistributeDialog
      v-model:dialogFormVisible="dialogStopVisible"
      :logOut="false"
      :api="enabledCoupon"
      :operateLogType="'COUPON_MANAGEMENT'"
      :operateType="`停用了“${dataInfo?.name}”的优惠`"
      :showContent="'couponStopStart'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'确认停用'"
      :couponTarget="'stopCoupon'"
      :dataInfo="dataInfo"
      :width="400"
      @reset="dialogStopVisible = false"
    />
    <DistributeDialog
      v-model:dialogFormVisible="dialogStartVisible"
      :logOut="false"
      :api="enabledCoupon"
      :operateLogType="'COUPON_MANAGEMENT'"
      :operateType="`启用了“${dataInfo?.name}”的优惠`"
      :showContent="'couponStopStart'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'确认启用'"
      :couponTarget="'startCoupon'"
      :dataInfo="dataInfo"
      :width="400"
      @reset="dialogStartVisible = false"
    />
    <AddressDialog
      :id="dataInfo?.id"
      v-model:dialogFormVisible="dialogAddressVisible"
      :logOut="false"
      :api="periodOpenGroupOrder"
      :textLeftBtn="'返回'"
      :title="'领取地址'"
      :width="300"
      @reset="dialogAddressVisible = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    .search-form {
      flex: 1;
    }
    .button {
      display: flex;
      justify-content: flex-end;
      margin-left: 20px;
      flex-wrap: wrap;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.upload-demo {
  margin-bottom: 10px;
}

.template-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.phone-container {
  display: flex;
  align-items: center;

  .eye-icon {
    margin-left: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
// :deep(.el-tabs__active-bar) {
//     background-color: transparent;
//   }
:deep(.el-tabs__nav-wrap:after) {
  background-color: transparent;
}
@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

<script setup>
import { ref, onMounted, onActivated, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseFindId,
  leaderLecturerFind,
  coursePeriodOffline,
  coursePeriodOnline,
  coursePeriodDelete,
  periodOpenGroupOrder,
  courseDelete,
  deletIds,
  coursePeriodPostpone
} from "@/api/course.js";
import { periodcancelReview, coursePeriodAll } from "@/api/period.js";
import { draftGetCount } from "@/api/drafts.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import OrderDialog from "@/components/Base/orderDialog.vue";
import notSaleDialog from "@/components/Base/notSaleDialog.vue";
import rescheduleDialog from "@/components/Base/rescheduleDialog.vue";
import {
  Warning,
  Document,
  User,
  Calendar,
  InfoFilled
} from "@element-plus/icons-vue";
import { AUDIT_ENUM } from "@/utils/enum";
import { ImageThumbnail } from "@/utils/imageProxy.js";
defineOptions({
  name: "CourseDetails"
});

const isRescheduleDisabled = row => {
  const disabledStates = ["PENDING_REVIEW", "APPROVED"];
  return disabledStates.includes(row.postponeAuditState);
};

// 获取改期按钮的tooltip内容
const getRescheduleTooltip = row => {
  if (row.postponeAuditState === "PENDING_REVIEW") {
    return "申请改期中";
  } else if (row.postponeAuditState === "APPROVED") {
    return "已改期";
  }
  return "";
};
onActivated(() => {
  getTablePeriodList();
  getTableList();
  draftGetCountApi();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

// 课程信息配置项
const courseInfoConfig = ref([
  {
    id: "courseName",
    label: "课程名",
    value: "",
    key: "name"
  },
  {
    id: "courseId",
    label: "课程ID",
    value: "",
    key: "id"
  },
  {
    id: "courseType",
    label: "课程类型",
    value: "",
    key: "courseType.name"
  },
  {
    id: "courseStatus",
    label: "课程状态",
    value: "",
    key: "freeze",
    formatter: data => (data.freeze === false ? "正常" : "冻结"),
    style: data => ({
      color: data.freeze === false ? "#67c23a" : "#f56c6c"
    })
  },
  {
    id: "createdAt",
    label: "创建时间",
    value: "",
    key: "createdAt",
    formatter: data => formatTime(data.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
  },
  {
    id: "ageRange",
    label: "课程年龄段",
    value: "",
    key: "ageRange",
    formatter: data => {
      if (data.minAge && data.maxAge) {
        return `${data.minAge}-${data.maxAge}岁`;
      } else if (data.minAge) {
        return `${data.minAge}岁以上`;
      } else if (data.maxAge) {
        return `${data.maxAge}岁以下`;
      }
      return "--";
    }
  }
]);

// 保持原有的tableHeader用于兼容性（如果其他地方还在使用）
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "课程状态",
    value: "",
    width: "107px"
  },
  {
    id: "7",
    label: "课程简介",
    value: "",
    width: "107px"
  }
]);
// 表格
const tableData = ref([]);
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  coursePeriodState: "all",
  leadersId: 0,
  lecturersId: 0,
  buyType: "all",
  reviewState: "all"
});
const url = ref();

const srcList = ref([]);
// 课程状态
const stateOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "NOT_LISTED",
    label: "未上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
    // label: "审核中"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
    // label: "审核中"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 审核状态
const auditOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核中"
  },
  {
    value: "ONLINE_PASS",
    label: "上架通过"
  },
  {
    value: "ONLINE_REJECT",
    label: "上架驳回"
    // label: "审核中"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核中"
  },
  {
    value: "OFFLINE_PASS",
    label: "下架通过"
    // label: "审核中"
  },
  {
    value: "OFFLINE_REJECT",
    label: "下架驳回"
    // label: "审核中"
  },
  {
    value: "NONE",
    label: "无"
  }
];
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};

// 获取课期状态颜色
const getCoursePeriodStateColor = state => {
  const colorMap = {
    NOT_LISTED: "#9A9A9A", // 无/未上架 - 灰色
    ONLINE: "#4095E5", // 上架 - 蓝色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE: "#FF6161", // 下架 - 红色
    OFFLINE_UNDER_REVIEW: "#FF6161", // 下架审核 - 红色
    COMPLETED: "#4095E5" // 已完成 - 蓝色
  };
  return colorMap[state] || "#9A9A9A";
};

// 获取审核状态颜色
const getAuditStateColor = state => {
  const colorMap = {
    NONE: "#9A9A9A", // 无 - 灰色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    ONLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    OFFLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    ONLINE_REJECT: "#FF6161", // 审核驳回 - 红色
    OFFLINE_REJECT: "#FF6161" // 审核驳回 - 红色
  };
  return colorMap[state] || "#9A9A9A";
};
// 讲师
const teacherOptions = ref([{ label: "全部", value: 0 }]);
// 领队
const leaderOptions = ref([{ label: "全部", value: 0 }]);
// 领队讲师查询
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type === 2) {
      // console.log("🍧-----res-----", res);
      let res1 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      teacherOptions.value = teacherOptions.value.concat(res1);
      // console.log("🍧----- teacherOptions.value-----", teacherOptions.value);
    } else {
      let res2 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      leaderOptions.value = leaderOptions.value.concat(res2);
      // console.log("🐠-----leaderOptions-----", leaderOptions);
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 购买类型
const typeOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ORDINARY",
    label: "普通单"
  },
  {
    value: "PRIVATE_DOMAIN_GROUP_ORDER",
    label: "团购单"
  }
];
// 重置
const setData = () => {
  params.value.page = 1;
  form.value = {
    startTime: "",
    endTime: "",
    coursePeriodState: "all",
    leadersId: "",
    lecturersId: "",
    buyType: "all",
    reviewState: "all"
  };
  pickTime.value = "";
  getTablePeriodList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTablePeriodList();
};
const pickTime = ref("");
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = value[1];
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    // console.log('🎁result--------333---------------------->',result);
    // 更新新的课程信息配置项
    courseInfoConfig.value.forEach(config => {
      if (config.formatter) {
        config.value = config.formatter(result);
      } else {
        // 支持嵌套属性访问，如 courseType.name
        const keys = config.key.split(".");
        let value = result;
        for (const key of keys) {
          value = value?.[key];
        }
        config.value = value || "--";
      }
    });
    tableHeader.value[6].value = result.introduction || "--";
    tableHeader.value[0].value = result.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType.name || "--";
    // if (result.minPeopleNumber && result.maxPeopleNumber) {
    //   tableHeader.value[4].value =
    //     result.minPeopleNumber + "-" + result.maxPeopleNumber;
    // } else if (result.minPeopleNumber) {
    //   tableHeader.value[4].value = result.minPeopleNumber;
    // } else if (result.maxPeopleNumber) {
    //   tableHeader.value[4].value = result.maxPeopleNumber;
    // } else {
    //   tableHeader.value[4].value = "--";
    // }
    tableHeader.value[4].value = result.tags?.join("、") || "--";
    tableHeader.value[5].value = result.freeze === false ? "正常" : "冻结";
    if (result.files?.length) {
      srcList.value = [];
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    // console.log('🎁srcList.value------------------------------>',srcList.value);
    // tableHeader.value[5].value = result.complex?.name || "--";
    useCourseStore.saveCourseInfo(result);
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const isOffline = ref(true);
// 获取课期列表信息
const gettLoading = ref(false);
const getTablePeriodList = async data => {
  if (gettLoading.value) {
    return;
  }
  gettLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: route.query.id
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.coursePeriodState === "all") {
    delete paramsData.coursePeriodState;
  }
  if (form.value.buyType === "all") {
    delete paramsData.buyType;
  }
  if (form.value.reviewState === "all") {
    delete paramsData.reviewState;
  }
  const [err, result] = await requestTo(coursePeriodAll(paramsData));
  if (result) {
    tableData.value = result?.content;
    // result?.content.forEach(it => {
    //   if (
    //     it.coursePeriodState === "COMPLETED" ||
    //     it.coursePeriodState === "OFFLINE_UNDER_REVIEW" ||
    //     it.coursePeriodState === "ONLINE" ||
    //     it.coursePeriodState === "ONLINE_UNDER_REVIEW"
    //   ) {
    //     isOffline.value = false;
    //   }
    // });
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  gettLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTablePeriodList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTablePeriodList();
};
// 清除数据
const clearEvt = val => {
  if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  }
  // params.value.page = 1;
  getTablePeriodList();
};
//编辑基本信息
const editinfo = () => {
  router.push({
    path: "/course/courseEdite",
    query: {
      type: "edite",
      id: route.query.id
    }
  });
};

// 复制
const copyEvt = row => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/course/copyCourseSchedule",
    query: { courseId: route.query.id, periodId: row.id }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};

// 详情
const detailEvt = row => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/course/courseDetails/currentDetails",
    query: { courseId: route.query.id, periodId: row.id }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};
const operateLog = ref({});
const isNotSaleDialog = ref(false); //下架申请弹窗
const notSaleObj = ref({});
const isRescheduleDialog = ref(false); //申请改期弹窗
const rescheduleObj = ref({});
// 下架
const notSaleEvt = row => {
  notSaleObj.value.id = row.id;
  notSaleObj.value.name = row.name;
  isNotSaleDialog.value = true;
};
const updateData = () => {
  getTablePeriodList();
};

// 申请改期
const rescheduleEvt = row => {
  rescheduleObj.value.id = row.id;
  rescheduleObj.value.name = row.name;
  isRescheduleDialog.value = true;
};

// 申请改期确认
const handleRescheduleConfirm = async data => {
  try {
    const params = {
      id: rescheduleObj.value.id,
      postponeOpenTime: data.postponeOpenTime,
      reason: data.reason
    };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: "申请改期了课期",
      operatorTarget: rescheduleObj.value.name
    };
    const { code, msg } = await coursePeriodPostpone(params, operateLog);

    if (code == 200) {
      ElMessage.success("申请改期成功，等待平台审核");
      isRescheduleDialog.value = false;
      getTablePeriodList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    ElMessage.error(error);
  }
};

// 是否上下架
const removeEvt = (row, bool) => {
  let freezeText = "课程上架申请，提交后等待平台审核。";
  let title = "上架申请";
  ElMessageBox.confirm(`${freezeText}`, `${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      // console.log("🐬-----row-----", row);
      isFreezeApi(row, bool);
    })
    .catch(() => {});
};
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row?.id
  };
  let api = coursePeriodOnline;
  operateLog.value = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `申请上架“${row.name}”课期`
    // operatorTarget: form.value.name,
  };
  const { code, msg } = await api(params, operateLog.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "上架申请成功"
    });
    getTablePeriodList();
  } else {
    if (code === 30039) {
      ElMessage({
        type: "error",
        message: `上架申请失败，${msg}，请去编辑`
      });
    } else if (code === 30040) {
      ElMessage({
        type: "error",
        message: `上架申请失败，${msg}，请去检查`
      });
    } else if (code === 30034) {
      ElMessage({
        type: "error",
        message: `上架申请失败，开课时间已过期`
      });
    } else {
      ElMessage({
        type: "error",
        message: `上架申请失败，${msg}`
      });
    }
  }
};
// 删除课期
const deleteEvt = val => {
  ElMessageBox.confirm(`确定要删除该课期吗？`, "删除课期", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${val.name}”课期`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(coursePeriodDelete({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getTablePeriodList();
      } else {
        ElMessage.error("删除失败");
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 删除课程
const deleteCourseEvt = () => {
  ElMessageBox.confirm(`确定要删除该课程吗？`, "删除课程", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${tableHeader.value[0].value}”课程`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(
        courseDelete({ id: Number(route.query.id) }, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("删除成功");
        router.replace("/course/courseManage");
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 团购弹窗
const dialogFormVisible = ref(false);
const periodValue = ref(0);
const periodName = ref("");
// 开启团购
const openGroupOrder = row => {
  periodValue.value = row.id;
  periodName.value = row.name;
  dialogFormVisible.value = true;
};
// 团购分享
const groupOrderShare = id => {
  router.replace({
    path: "/course/currentDetails/groupOrder",
    query: { periodId: id }
  });
};

// 取消审核（撤销申请）
const cancel = val => {
  let title =
    val.coursePeriodState === "OFFLINE_UNDER_REVIEW"
      ? "下架申请"
      : val.coursePeriodState === "ONLINE_UNDER_REVIEW"
        ? "上架申请"
        : "申请";
  ElMessageBox.confirm(`确定要撤销该课期的${title}吗？`, `撤销${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `撤销了“${val.name}”课期的${title}`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(periodcancelReview({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("撤销申请成功");
        getTablePeriodList();
      } else {
        ElMessage.error(`撤销申请失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("撤销申请失败");
      }
    })
    .catch(() => {});
};
// 获取领队讲师姓名
const getName = val => {
  let res = [];
  if (!val?.length) return;
  val.map(item => {
    res.push(item.name);
  });
  return res.join("、");
};
// 批量删除
let batchDeleteArr = ref([]);
const tableRef = ref(null);
let isSelection = ref(false);
const selectable = row => {
  if (
    (row.coursePeriodState === "OFFLINE" && row.isExistInformation === false) ||
    row.coursePeriodState === "NOT_LISTED"
  ) {
    return row.id;
  }
};
const getRowKeys = row => {
  return row.id;
};
const isHandleSelectionChange = () => {
  isSelection.value = !isSelection.value;
  if (isSelection.value) {
    clearDelete();
  }
};
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  ids.value = [];
  course.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
let ids = ref([]);
let course = ref([]);
const batchDelete = () => {
  batchDeleteArr.value.forEach(item => {
    ids.value.push(item.id);
    course.value.push(item.name);
  });
  if (ids.value.length === 0) {
    ElMessage.error("请选择要删除的课程");
    return;
  }
  ElMessageBox.confirm(
    `确定要删除“${course.value.join("、")}”课期吗？`,
    "删除课期",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  )
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${course.value.join("、")}”课期`
      };
      let [err, res] = await to(deletIds({ ids: ids.value }, operateLog));
      if (res.code === 200 && res.data.length === 0) {
        ElMessage.success("删除成功");
        batchDeleteArr.value = [];
        ids.value = [];
        course.value = [];
        tableRef.value.clearSelection();
        getTablePeriodList();
      } else {
        let coursePeriodName = [];
        let reason = [];
        res.data.forEach(item => {
          coursePeriodName.push(item.coursePeriodName);
          reason.push(item.reason);
        });
        // ElMessage.error(`删除失败,${res.msg}`);
        ElMessageBox.confirm(
          `${coursePeriodName.map((item, i) => `${item}:${reason[i]}`).join("、")}`,
          "删除失败",
          {
            cancelButtonText: "取消"
          }
        );
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
// 退出批量删除
const clearDelete = () => {
  tableRef.value.clearSelection();
  batchDeleteArr.value = [];
};
onMounted(async () => {
  getTableList();
  getTablePeriodList();
  draftGetCountApi();
  await leaderFindApi(2);
  leaderFindApi(3);
});
const draftsNum = ref(0);

// 伸缩状态管理
const isCollapsed = ref(false);

// 切换伸缩状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 计算课程信息展示数据
const courseMetaRows = computed(() => {
  // 获取所有有效的配置项（排除课程名，且有数据的）
  const validConfigs = courseInfoConfig.value
    .filter(
      item => item.id !== "courseName" && item.value && item.value !== "--"
    )
    .map(item => ({ ...item }));

  // 按每行3列分组
  const rows = [];
  for (let i = 0; i < validConfigs.length; i += 3) {
    rows.push(validConfigs.slice(i, i + 3));
  }

  return rows;
});
// 查询草稿数量
const draftGetCountApi = async () => {
  let [err, res] = await to(draftGetCount());
  if (res.code === 200) {
    draftsNum.value = res?.data?.draftCount || 0;
    // console.log("🍭-----res-----", res.data.draftCount);
  } else {
    console.log("🎁-----err-----", err);
  }
};
</script>

<template>
  <div class="containers">
    <div class="content_top">
      <!-- 伸缩按钮 - 固定在右上角 -->
      <el-button class="collapse-btn-fixed" @click="toggleCollapse">
        <el-icon :class="{ 'rotate-180': !isCollapsed }">
          <svg viewBox="0 0 1024 1024" width="16" height="16">
            <path
              d="M512 714.666667L277.333333 480l42.666667-42.666667L512 629.333333l192-192L746.666667 480z"
              fill="currentColor"
            />
          </svg>
        </el-icon>
      </el-button>
      <!-- 课程信息卡片 -->
      <el-card class="course-card" shadow="never">
        <div class="course-header" :class="{ collapsed: isCollapsed }">
          <div v-show="!isCollapsed" class="course-cover">
            <el-image
              :src="ImageThumbnail(url, '280x')"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="srcList"
              :hide-on-click-modal="true"
              show-progress
              :initial-index="4"
              fit="cover"
              class="cover-image"
            />
          </div>

          <div class="course-info">
            <!-- 收缩状态下的水平布局 -->
            <div v-if="isCollapsed" class="collapsed-header">
              <div class="course-title-collapsed">
                <h2>
                  {{
                    courseInfoConfig.find(item => item.id === "courseName")
                      ?.value || "--"
                  }}
                </h2>
              </div>
              <div class="course-actions-collapsed">
                <el-button
                  v-if="tableHeader[5].value === '正常'"
                  @click="editinfo"
                >
                  编辑课程信息
                </el-button>
                <el-button
                  @click="
                    router.push({
                      path: '/course/allEvaluate',
                      query: { courseId: route.query.id }
                    })
                  "
                >
                  查看全部评价
                </el-button>
                <el-button type="danger" @click="deleteCourseEvt">
                  删除课程
                </el-button>
              </div>
            </div>

            <!-- 展开状态下的原始布局 -->
            <template v-else>
              <div class="course-title">
                <h2>
                  {{
                    courseInfoConfig.find(item => item.id === "courseName")
                      ?.value || "--"
                  }}
                </h2>
              </div>

              <!-- 课程详细信息区域 - 可收缩 -->
              <div class="course-details">
                <!-- 课程亮点标签 -->
                <div class="course-tags">
                  <template
                    v-if="tableHeader[4].value && tableHeader[4].value !== '--'"
                  >
                    <el-tag
                      v-for="tag in tableHeader[4].value.split('、')"
                      :key="tag"
                      type="primary"
                      effect="light"
                      class="tag-item"
                      size="small"
                    >
                      {{ tag }}
                    </el-tag>
                  </template>
                </div>

                <!-- 课程简介 -->
                <div class="course-intro">
                  <div
                    v-if="tableHeader[6].value && tableHeader[6].value !== '--'"
                    class="intro-label"
                  >
                    简介：
                  </div>
                  <div class="intro-content">
                    <p
                      v-if="
                        tableHeader[6].value && tableHeader[6].value !== '--'
                      "
                    >
                      {{ tableHeader[6].value }}
                    </p>
                  </div>
                </div>

                <div class="course-meta">
                  <div
                    v-for="(row, rowIndex) in courseMetaRows"
                    :key="rowIndex"
                    class="meta-row"
                  >
                    <div
                      v-for="config in row"
                      :key="config.id"
                      class="meta-item"
                    >
                      <span class="meta-label">{{ config.label }}：</span>
                      <span
                        class="meta-value"
                        :style="
                          config.style
                            ? config.style(useCourseStore.courseInfo || {})
                            : {}
                        "
                      >
                        {{ config.value }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 展开状态下的功能按钮（保持原来位置） -->
          <div v-show="!isCollapsed" class="course-actions-right">
            <el-button v-if="tableHeader[5].value === '正常'" @click="editinfo">
              编辑课程信息
            </el-button>
            <el-button
              @click="
                router.push({
                  path: '/course/allEvaluate',
                  query: { courseId: route.query.id }
                })
              "
            >
              查看全部评价
            </el-button>
            <el-button type="danger" @click="deleteCourseEvt">
              删除课程
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    <div class="content_bottom">
      <!-- 新增：顶部操作行 -->
      <div class="content-top-actions">
        <div class="actions-title">
          <p>课期管理</p>
        </div>
        <div class="actions-buttons">
          <el-button
            v-if="tableHeader[5].value === '正常'"
            type="primary"
            style="margin-right: 12px"
            @click="
              router.push({
                path: '/course/coursePeriod/Create',
                query: {
                  courseId: route.query.id,
                  type: 'createPeriodDetail'
                }
              })
            "
          >
            新建课期
          </el-button>
          <el-button type="primary" @click="router.push('/course/drafts')">
            草稿箱 {{ `(${draftsNum})` }}
          </el-button>
        </div>
      </div>
      <!-- 分割线 -->
      <el-divider style="margin: 14px 0 20px 0" />
      <div class="con_search">
        <div class="search-form-container">
          <el-form :model="form" :inline="true" class="search-form">
            <el-form-item label="开课时间">
              <el-date-picker
                v-model="pickTime"
                type="daterange"
                start-placeholder="请选择开始时间"
                end-placeholder="请选择结束时间"
                value-format="x"
                @change="timeChange"
                @clear="clearEvt('time')"
              />
            </el-form-item>
            <el-form-item label="购买类型">
              <el-select
                v-model="form.buyType"
                style="width: 130px"
                placeholder="请选择"
                value-key="id"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="领队">
              <el-select
                v-model="form.leadersId"
                style="width: 130px"
                placeholder="请选择领队"
                value-key="id"
              >
                <el-option
                  v-for="item in leaderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="讲师">
              <el-select
                v-model="form.lecturersId"
                style="width: 130px"
                placeholder="请选择讲师"
                value-key="id"
              >
                <el-option
                  v-for="item in teacherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="课期状态">
              <el-select
                v-model="form.coursePeriodState"
                style="width: 130px"
                placeholder="请选择"
                :empty-values="[null, undefined]"
                :value-on-clear="null"
              >
                <el-option
                  v-for="item in stateOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select
                v-model="form.reviewState"
                style="width: 130px"
                placeholder="请选择"
                :empty-values="[null, undefined]"
                :value-on-clear="null"
              >
                <el-option
                  v-for="item in auditOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <div class="flex">
                <el-button
                  type="primary"
                  style="margin-right: 12px"
                  @click="searchData"
                >
                  搜索
                </el-button>
                <el-button style="margin-right: 12px" @click="setData">
                  重置
                </el-button>
                <!-- <el-button type="danger" @click="isHandleSelectionChange()">
                  {{ isSelection === false ? "批量删除" : "退出批量删除" }}
                </el-button> -->
                <el-button type="danger" @click="isHandleSelectionChange()">
                  {{ isSelection === false ? "批量删除" : "退出批量删除" }}
                  <el-tooltip
                    content="未申请上架的课期可删除，其他课期不可删除"
                    placement="top"
                    effect="light"
                    :popper-style="{ maxWidth: '300px' }"
                  >
                    <el-icon style="margin-left: 8px; cursor: pointer">
                      <Warning />
                    </el-icon>
                  </el-tooltip>
                </el-button>
              </div>
            </el-form-item>
          </el-form>

          <!-- <div class="search-buttons">
            <el-button type="primary" @click="searchData"> 搜索 </el-button>
            <el-button @click="setData"> 重置 </el-button>
            <el-button
              type="danger"
              style="margin-right: 10px"
              @click="isHandleSelectionChange()"
            >
              {{ isSelection === false ? "批量删除" : "退出批量删除" }}
            </el-button>
          </div> -->
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <!-- <div class="operation-buttons"> -->
      <!-- 移除新建课期按钮，已移动到顶部 -->
      <!-- <el-button
          type="danger"
          style="margin-right: 10px"
          @click="isHandleSelectionChange()"
        >
          {{ isSelection === false ? "批量删除" : "退出批量删除" }}
        </el-button>
      </div> -->

      <div class="con_table">
        <el-table
          ref="tableRef"
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          :row-key="getRowKeys"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="isSelection === true"
            :selectable="selectable"
            :reserve-selection="true"
            type="selection"
            width="55"
          />
          <el-table-column prop="termNumber" label="期号" width="80" fixed>
            <template #default="scope">
              <el-text>
                {{ scope.row.termNumber || 0 }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="课期名"
            align="left"
            show-overflow-tooltip
            width="320"
            fixed
          >
            <template #default="scope">
              {{ scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="coursePeriodState"
            label="课期状态"
          >
            <!-- <template #default="scope">
              <div>
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template> -->
            <template #default="scope">
              <div
                v-if="
                  scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                  scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    scope.row.offlineType === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制取消定制，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                  :popper-style="{ maxWidth: '500px' }"
                >
                  <div
                    class="status-tag"
                    :style="{
                      color: getCoursePeriodStateColor(
                        scope.row.coursePeriodState
                      )
                    }"
                  >
                    {{ getSatte(scope.row.coursePeriodState) || "--" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="status-tag"
                :style="{
                  color: getCoursePeriodStateColor(scope.row.coursePeriodState)
                }"
              >
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="auditState"
            label="审核状态"
            align="left"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.reviewState === 'OFFLINE_REJECT' ||
                  scope.row.reviewState === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="scope.row.opinion ? scope.row.opinion : '无'"
                  placement="bottom"
                  effect="light"
                  :popper-style="{ maxWidth: '600px' }"
                >
                  <div
                    class="status-tag"
                    :style="{
                      color: getAuditStateColor(scope.row.reviewState)
                    }"
                  >
                    {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="status-tag"
                :style="{ color: getAuditStateColor(scope.row.reviewState) }"
              >
                {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="buyType" label="购买类型" align="left">
            <template #default="scope">
              <div>
                {{
                  scope.row.buyType === "ORDINARY"
                    ? "普通单"
                    : scope.row.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
                      ? "团购单"
                      : "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="leaders"
            label="领队"
            align="left"
            width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.leaders) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="lecturers"
            label="讲师"
            align="left"
            width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.lecturers) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="openTime"
            label="开课时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdAt"
            label="创建时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="teachers"
            label="专家"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div v-if="scope.row.lecturers?.length > 0">
                <div v-for="item in scope.row.lecturers">
                  <div
                    v-if="
                      scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                      scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                    "
                  >
                    <el-tooltip
                      class="box-item"
                      title=""
                      :content="item.name"
                      placement="bottom"
                      effect="light"
                    >
                      <div class="state-reject">
                        {{ getSatte(scope.row.coursePeriodState) || "--" }}
                        <el-icon style="color: red; margin-left: 3px">
                          <Warning />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                  <div
                    v-if="
                      scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                      scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                    "
                  >
                    <el-tooltip
                      class="box-item"
                      title=""
                      :content="item.name"
                      placement="bottom"
                      effect="light"
                    >
                      <div class="state-reject">
                        {{ getSatte(scope.row.coursePeriodState) || "--" }}
                        <el-icon style="color: blue; margin-left: 3px">
                          <Warning />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div v-else>{{ getName(scope.row.lecturers) || "--" }}</div>
            </template>
          </el-table-column> -->
          <el-table-column
            prop="address"
            fixed="right"
            label="操作"
            align="left"
            width="328px"
          >
            <template #default="{ row }">
              <div class="option">
                <div class="btnse" @click="detailEvt(row)">详情</div>
                <div
                  v-if="
                    row.coursePeriodState === 'OFFLINE' ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="btnse"
                  @click="
                    router.push({
                      path: '/course/coursePeriodEdite',
                      query: {
                        periodId: row.id,
                        type: 'edite',
                        courseId: route.query.id,
                        fromPage: 'courseDetail'
                      }
                    })
                  "
                >
                  编辑
                </div>
                <div class="btnse" @click="copyEvt(row)">复制</div>
                <div
                  v-if="
                    row.coursePeriodState === 'ONLINE' &&
                    row.buyType === 'ORDINARY'
                  "
                  class="btnse"
                  @click="notSaleEvt(row)"
                >
                  下架申请
                </div>
                <div
                  v-else-if="
                    row.coursePeriodState === 'OFFLINE' ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="nofreeze"
                  @click="removeEvt(row, false)"
                >
                  上架申请
                </div>
                <div
                  v-if="
                    row.buyType === 'ORDINARY' &&
                    (row.coursePeriodState === 'OFFLINE' ||
                      row.coursePeriodState === 'NOT_LISTED')
                  "
                  class="btnse1"
                  @click="openGroupOrder(row)"
                >
                  课程定制
                </div>
                <div
                  v-if="
                    row.buyType !== 'ORDINARY' &&
                    row.coursePeriodState === 'ONLINE'
                  "
                  class="btnse"
                  @click="groupOrderShare(row.id)"
                >
                  团购分享
                </div>
                <div
                  v-if="
                    (row.coursePeriodState === 'OFFLINE' &&
                      row.isExistInformation === false) ||
                    row.coursePeriodState === 'NOT_LISTED'
                  "
                  class="btnse1"
                  @click="deleteEvt(row)"
                >
                  删除
                </div>
                <div
                  v-if="
                    (row.coursePeriodState === 'OFFLINE_UNDER_REVIEW' ||
                      row.coursePeriodState === 'ONLINE_UNDER_REVIEW') &&
                    row.isTransmit === false
                  "
                  class="btnse"
                  @click="cancel(row)"
                >
                  撤销申请
                </div>
                <el-tooltip
                  :disabled="!isRescheduleDisabled(row)"
                  :content="getRescheduleTooltip(row)"
                  placement="right"
                >
                  <div
                    v-if="
                      row.coursePeriodState === 'ONLINE' &&
                      ((row.buyType === 'ORDINARY' &&
                        row.reviewState === 'ONLINE_PASS') ||
                        (row.buyType === 'PRIVATE_DOMAIN_GROUP_ORDER' &&
                          row.reviewState === 'NONE'))
                    "
                    :class="[
                      'btnse',
                      { 'btnse-disabled': isRescheduleDisabled(row) }
                    ]"
                    @click="!isRescheduleDisabled(row) && rescheduleEvt(row)"
                  >
                    申请改期
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <!-- 批量删除 -->
        </el-table>
        <div v-if="isSelection === true" class="con_delete">
          <el-button type="primary" @click="batchDelete">确认删除</el-button>
          <template v-if="batchDeleteArr.length > 0">
            <span class="delete-tip">已选{{ batchDeleteArr.length }}个课期：</span>
            <span
              v-for="item in batchDeleteArr"
              :key="item.id"
              class="delete-tip"
              >{{ item.name }}&nbsp;&nbsp;</span>
          </template>
        </div>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <OrderDialog
      :id="periodValue"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="periodOpenGroupOrder"
      :operateLogType="'COURSE_MANAGEMENT'"
      :operateType="`定制了“${periodName}课期”的课程`"
      :logOut="false"
      :showContent="'groupOrder'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'课程定制确认'"
      :marginLeft="'10px'"
      @reset="dialogFormVisible = false"
    />
    <notSaleDialog
      :id="notSaleObj.id"
      v-model:dialogFormVisible="isNotSaleDialog"
      :api="coursePeriodOffline"
      :operateLogType="'COURSE_MANAGEMENT'"
      :operateType="`申请下架“${notSaleObj.name}”课期`"
      :logOut="false"
      :showContent="'notSale'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'下架申请'"
      :marginLeft="'10px'"
      @reset="isNotSaleDialog = false"
      @update-data="updateData"
    />
    <rescheduleDialog
      v-model:dialogFormVisible="isRescheduleDialog"
      :title="`申请改期 - ${rescheduleObj.name || ''}`"
      :textRightBtn="'确定'"
      :textLeftBtn="'取消'"
      @reset="isRescheduleDialog = false"
      @confirm="handleRescheduleConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100vh;
  max-height: 88vh;
  display: flex;
  flex-direction: column;
  // padding: 24px;
  background: #f0f2f5;
  overflow: hidden;

  .content_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
    flex-shrink: 0;
    position: relative;

    // 固定在右上角的伸缩按钮
    .collapse-btn-fixed {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 10;
      width: 36px;
      height: 36px;
      padding: 0;
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      background: #fff;
      color: #606266;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        border-color: #409eff;
        color: #409eff;
        background: rgba(64, 158, 255, 0.05);
      }

      &:active {
        transform: translateY(0);
      }

      .el-icon {
        font-size: 16px;
        transition: transform 0.3s ease;

        &.rotate-180 {
          transform: rotate(180deg);
        }
      }
    }

    .course-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left-actions {
        display: flex;
        gap: 10px;
      }

      .right-actions {
        display: flex;
        gap: 10px;
      }
    }
    .course-actions-right {
      display: flex;
      align-self: flex-end;
      flex-wrap: wrap;
      gap: 12px;
    }

    .course-card {
      border-radius: 0;
      border: none;
      :deep(.el-card__body) {
        padding: 0;
      }
      .course-header {
        display: flex;
        gap: 36px;
        transition: all 0.3s ease;

        &.collapsed {
          gap: 0;
        }

        .course-cover {
          display: flex;
          flex-shrink: 0;

          .cover-image {
            width: 280px;
            height: 180px;
            border-radius: 8px;
            overflow: hidden;
            border-radius: 8px !important;
            position: relative;

            :deep(.el-image__inner) {
              width: 100% !important;
              height: auto !important;
              position: absolute;
              top: 50%;
              left: 0;
              transform: translateY(-50%);
              object-fit: cover;
            }
          }
        }

        .course-info {
          flex: 1;

          // 收缩状态下的水平布局
          .collapsed-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 36px; // 与伸缩按钮高度一致
            padding-right: 60px; // 给伸缩按钮留出空间

            .course-title-collapsed {
              flex: 1;

              h2 {
                font-size: 20px; // 收缩状态下稍微减小字体
                font-weight: 600;
                color: #1a1a1a;
                margin: 0;
                line-height: 36px; // 与容器高度一致，实现垂直居中
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .course-actions-collapsed {
              display: flex;
              gap: 8px;
              flex-shrink: 0;

              .el-button {
                height: 32px;
                padding: 0 12px;
                font-size: 13px;
              }
            }
          }

          // 展开状态下的原始样式（保持不变）
          .course-title {
            margin-bottom: 12px;

            h2 {
              font-size: 24px;
              font-weight: 600;
              color: #1a1a1a;
              margin: 0;
              line-height: 1.3;
            }
          }

          .course-details {
            transition: all 0.3s ease;
          }

          .course-intro {
            margin-bottom: 24px;
            min-height: 23px; // 确保固定高度，即使没有简介内容
            display: flex;
            align-items: flex-start;

            .intro-label {
              width: 50px;
              color: #666;
              font-size: 14px;
              flex-shrink: 0;
              line-height: 1.5;
              margin-top: 2px;
            }

            .intro-content {
              flex: 1;

              p {
                margin: 0;
                color: #333;
                font-size: 14px;
                line-height: 1.6;
                word-break: break-all;
                text-align: justify; // 两端对齐
              }
            }
          }

          .course-tags {
            margin-bottom: 4px;
            min-height: 24px; // 确保固定高度，即使没有标签

            .tag-item {
              margin-right: 8px;
              margin-bottom: 8px;
            }
          }

          .course-meta {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .meta-row {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 16px 32px;
              align-items: center;
              margin-bottom: 0;

              .meta-item {
                display: flex;
                align-items: flex-start;

                .meta-label {
                  width: 90px;
                  color: #666;
                  font-size: 14px;
                  flex-shrink: 0;
                  text-align: left;
                  line-height: 1.5;
                }

                .meta-value {
                  color: #333;
                  font-size: 14px;
                  font-weight: 500;
                  flex: 1;
                  min-width: 80px;
                  word-break: break-all;
                  line-height: 1.5;
                }
              }
            }
          }
        }
      }
    }
  }

  .content_bottom {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: #fff;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-top-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .actions-title {
        p {
          margin: 0;
          font-size: 16px;
          font-weight: 700;
          color: #303133;
        }
      }

      .actions-buttons {
        display: flex;
      }
    }
  }

  :deep(.el-button + .el-button) {
    margin: 0;
  }

  // :deep(.el-button--primary) {
  //   width: 100px;
  // }
}

.con_search {
  width: 100%;
  height: fit-content;
  flex-shrink: 0;
  // margin-top: 40px;

  .search-form-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;

    .search-form {
      flex: 1;
    }
  }

  .btn_search {
    display: flex;
    justify-content: space-between;
    width: 140px;
  }
}

.operation-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}
.state-reject {
  width: 40px;
  display: flex;
  // justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.status-tag {
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-weight: 500;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

.con_table {
  // width: calc(100% - 25px);
  // min-height: 500px;
  margin-bottom: 24px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // margin-left: 25px;
  .option {
    display: flex;
    white-space: nowrap;
    .btnse {
      display: flex;
      margin-right: 16px;
      color: #4095e5;
      cursor: pointer;
      // white-space: nowrap;
    }
    .btnse-disabled {
      color: #a0cfff;
      cursor: not-allowed;
    }
    .nofreeze {
      color: #f56c6c;
      cursor: pointer;
      margin-right: 16px;
    }
    .btnse1 {
      // display: flex;
      margin-right: 16px;
      color: #f56c6c;
      cursor: pointer;
    }
  }
}

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  flex-shrink: 0;
  padding-top: 16px;
}
.delete-tip {
  font-size: 14px;
  &:nth-of-type(1) {
    margin-left: 10px;
  }
}
</style>

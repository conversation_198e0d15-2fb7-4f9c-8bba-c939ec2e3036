<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from "vue";
import dayjs from "dayjs";
import {
  freeCreateOrUpdate,
  createOrUpdateSpecification,
  findSpecificationByCoursePeriodId,
  findSpecificationNameOptionByCoursePeriodId,
  findFeeItemByCoursePeriodId,
  findSpecificationByCoursePeriodIdPrice,
  CoursePeriodIdByFree,
  updatePriceSetting
} from "@/api/period.js";
import {
  priceSettingFindByDraftId,
  saveDraftPriceSetting,
  findSpecificationNameOptionByDraftId,
  findSpecificationTableByDraftId,
  findSpecificationByDraftId,
  findFeeItemByDraftId,
  draftDelete,
  draftCoursePeriodCountByDraftId
} from "@/api/drafts.js";
import {
  getCouponFindAll,
  findCouponByCoursePeriodId,
  updateCoursePeriodCoupon
} from "@/api/coupon.js";
import {
  Warning,
  Close,
  DCaret,
  Switch,
  Sort,
  Plus,
  Delete,
  Edit,
  Rank,
  MagicStick
} from "@element-plus/icons-vue";
import { requestTo } from "@/utils/http/tool";
import { to, deepClone, removeEmptyValues } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { VueDraggable } from "vue-draggable-plus";
import { useRouter, useRoute } from "vue-router";
import { usePriceSet } from "../utils/priceHook";
import DescriptionList from "./descriptionList.vue";
import { aiNewPage } from "@/utils/aiTool.js";
import { courseStore } from "@/store/modules/course.js";
import { freeValue, refundValue } from "@/utils/defaultValue.js";

const props = defineProps({
  draftId: {
    type: Number,
    default: 0
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  }
});
const emites = defineEmits(["baseInfo"]);
const useCourseStore = courseStore();
const { list, getColumsData, setData, getValList } = usePriceSet();
const router = useRouter();
const route = useRoute();

const temcolumns = ref([]);
const temData = ref([]);
const tableWidth = ref(800);
const tableHeight = ref(60);
const textareaFree = ref(""); // 费用说明
const textareaRefund = ref(""); // 退款说明
const MAX_TABLE_HEIGHT = 500;
const ITEM_HEIGHT = 60;

// 当同一草稿下存在多个课期(count>1)时，禁用“前报名用户可以享受促销价”规则
const timeRuleDisabled = ref(false);

// 新的价格设置数据
const priceSettings = ref({
  coursePrice: "", // 课程费用
  materialPrice: "", // 材料费用
  priceType: "fixed", // 价格类型：fixed(是) 或 negotiable(否)
  priceDescription: "", // 费用说明
  // 促销价（纯前端）
  promotionEnabled: false, // 是否开启促销价
  promotionPrice: "", // 促销价
  ruleFirstNEnabled: false, // 规则1：前 N 名
  ruleFirstNCount: "", // N 值
  ruleTimeEnabled: false, // 规则2：时间范围
  ruleTimeRange: [] // [开始时间, 结束时间]
});

// 优惠券相关数据
const couponData = ref({
  list: [], // 优惠券列表
  selectedIds: [], // 已选中的优惠券ID列表
  pagination: {
    page: 0,
    size: 10,
    total: 0,
    totalPages: 0
  },
  loading: false,
  couponAvailable: false // 是否可以使用优惠券
});

// 优惠券表格列定义
const couponColumns = [
  { prop: "name", label: "优惠券名称", width: 150 },
  { prop: "couponTypeText", label: "优惠券类型", width: 120 },
  { prop: "feeTypeText", label: "优惠费用类型", width: 120 },
  { prop: "distributionTime", label: "发放时间", width: 180 },
  { prop: "useTime", label: "使用时间", width: 180 }
];

// 金额输入统一处理：允许输入小数，最多两位，允许输入临时态如 "1."，不强制转数值
const sanitizeMoneyInput = rawValue => {
  if (rawValue === undefined || rawValue === null) return "";
  let value = String(rawValue);
  // 仅保留数字与点
  value = value.replace(/[^\d.]/g, "");
  // 只保留第一个小数点
  const firstDot = value.indexOf(".");
  if (firstDot !== -1) {
    value =
      value.slice(0, firstDot + 1) +
      value.slice(firstDot + 1).replace(/\./g, "");
  }
  // 限制为两位小数
  if (firstDot !== -1) {
    const [intPart, decPart] = value.split(".");
    value = intPart + "." + (decPart ?? "").slice(0, 2);
  }
  // 去除前导零（保留如 0.x 场景）
  if (!value.startsWith("0.")) {
    value = value.replace(/^0+(\d)/, "$1");
  }
  // 不允许负数
  if (value.startsWith("-")) value = value.replace(/-/g, "");

  // 限制最大值为 1,000,000
  const numValue = Number(value);
  if (!isNaN(numValue) && numValue > 999999) {
    return "999999";
  }

  return value;
};

// 处理课程费用输入
const handleCoursePrice = value => {
  priceSettings.value.coursePrice = sanitizeMoneyInput(value);
  // 调整促销价上限（若已开启促销）
  if (priceSettings.value.promotionEnabled) {
    clampPromotionToCourse();
  }
};

// 处理材料费用输入
const handleMaterialPrice = value => {
  priceSettings.value.materialPrice = sanitizeMoneyInput(value);
};

// 将促销价限制在不高于课时费用
const clampPromotionToCourse = () => {
  const course = Number(priceSettings.value.coursePrice || 0);
  const promo = Number(priceSettings.value.promotionPrice || 0);
  if (!isNaN(course) && !isNaN(promo) && promo > course) {
    priceSettings.value.promotionPrice = sanitizeMoneyInput(
      priceSettings.value.coursePrice
    );
  }
};

// 处理促销价输入
const handlePromotionPrice = value => {
  priceSettings.value.promotionPrice = sanitizeMoneyInput(value);
  clampPromotionToCourse();
};

// 失去焦点时去除 .0 或 .00，规范为整数
const formatMoneyOnBlur = key => {
  const raw = priceSettings.value[key];
  if (raw === undefined || raw === null || raw === "") return;
  let s = String(raw);

  // 如果输入是单独的 "."，则重置为空
  if (s === ".") {
    priceSettings.value[key] = "";
    return;
  }

  // 如果以 "." 开头，则在前面补 "0"
  if (s.startsWith(".") && s.length > 1) {
    s = "0" + s;
  }

  // 若以单个点结尾，如 "12." 则去掉点
  if (/^\d+\.$/.test(s)) {
    s = s.slice(0, -1);
  }
  // x.0 或 x.00 → x
  const m = s.match(/^(\d+)\.0{1,2}$/);
  if (m) {
    s = m[1];
  }
  priceSettings.value[key] = s;
  // 若是促销价，失焦时也执行一次上限修正
  if (key === "promotionPrice" && priceSettings.value.promotionEnabled) {
    clampPromotionToCourse();
  }
};
// 数据更新
const handleUpdateData = () => {
  const columns = getColumsData();
  temcolumns.value = formatColumns(columns);
  temData.value = convertListToTemp(list.value) || [];
  updateTableHeight(temData.value.length);
};
const updateTableHeight = dataLength => {
  const calculatedHeight = Math.min(
    dataLength * ITEM_HEIGHT + 40,
    MAX_TABLE_HEIGHT
  );
  tableHeight.value = calculatedHeight;
};
// 添加规格
const addEvt = () => {
  // 检查当前组合数量是否已经超过500
  const currentCombinations = calculateCombinations(list.value);
  if (currentCombinations >= 500) {
    ElMessage.warning("规格组合数量不允许超过500");
    return;
  }

  const length = list.value.length + 1;
  list.value.push({
    name: `规格`,
    id: `${length}`,
    children: [
      {
        name: `默认`,
        id: `${length}-${1}`
      }
    ]
  });
  handleUpdateData();
};

// 添加条目
const addRowEvt = (item1, item2) => {
  // 检查当前组合数量是否已经超过500
  const currentCombinations = calculateCombinations(list.value);
  if (currentCombinations >= 500) {
    ElMessage.warning("规格组合数量不允许超过500");
    return;
  }

  list.value.forEach(item => {
    if (item.id === item1.id) {
      if (item.children && item.children.length > 0) {
        item.children.push({
          name: `默认`,
          id: `${item.id}-${item.children.length + 1}`
        });
      } else {
        item.children = [];
        item.children.push({
          name: `默认`,
          id: `${item.id}-${item.children.length + 1}`
        });
      }
    }
  });
  temData.value = convertListToTemp(list.value);
};

// 计算组合数量的辅助函数
const calculateCombinations = specList => {
  if (!specList || specList.length === 0) return 0;

  let totalCombinations = 1;
  specList.forEach(spec => {
    if (spec.children && spec.children.length > 0) {
      totalCombinations *= spec.children.length;
    }
  });

  return totalCombinations;
};

// 处理数据
// function convertListToTemp(list = []) {
//   const result = [];
//   function generateCombinations(index, currentCombination) {
//     if (index === list.length) {
//       const priceAndQu =
//         isEdit.value || route.query.draftId || props.draftId
//           ? getPriceAndQuantity(currentCombination)
//           : {
//               price: 0,
//               quantity: 1
//             };

//       if (priceAndQu.price === undefined || priceAndQu.price === null) {
//         priceAndQu.price = 0;
//       }
//       if (priceAndQu.quantity === undefined || priceAndQu.quantity === null) {
//         priceAndQu.quantity = 1;
//       }

//       result.push({ ...currentCombination, ...priceAndQu });
//       return;
//     }
//     const item = list[index];
//     if (item.children && item.children.length > 0) {
//       item.children.forEach(child => {
//         currentCombination[item.name] = child.name;
//         generateCombinations(index + 1, currentCombination);
//       });
//     } else {
//       currentCombination[item.name] = null;
//       generateCombinations(index + 1, currentCombination);
//     }
//   }
//   generateCombinations(0, {});
//   return result;
// }

// 获取价格和数量
// const getPriceAndQuantity = itemVal => {
//   const valueCombination = Object.values(itemVal);
//   console.log('🍭valueCombination------------------------------>',valueCombination);
//   console.log("🔍-----listVal.value-----", listVal.value);

//   // 获取完整的规格信息，包括索引位置
//   const specIndexes = {};

//   valueCombination.forEach((item, index) => {
//     specIndexes[index] = item;
//   });
//   console.log('🍪specIndexes------------------------------>',specIndexes);
//   // 根据规格位置和名称查找正确的ID
//   const codeList = [];
//   Object.entries(specIndexes).forEach(([index, name]) => {
//     // 获取当前规格的所有选项
//     const specOptions = listVal.value.filter((item, i, arr) => {

//       // 根据索引位置筛选正确的规格组
//       const isFirstGroup = index === "0" && i < arr.length / 2;
//       const isSecondGroup = index === "1" && i >= arr.length / 2;
//       return (isFirstGroup || isSecondGroup) && item.name === name;
//     });
//     console.log('🐬specOptions------------------------------>',specOptions);
//     if (specOptions.length > 0) {
//       codeList.push(specOptions[0].id);
//     }
//   });

//   const optionCode = codeList.join("-");
//   // console.log('🍪-----修正后的optionCode-----', optionCode);
//   const priceItem = priceInfo.value.find(it => it.optionCode === optionCode);
//   // console.log("🎁-----priceItem-----", priceItem);

//   // 确保返回的价格和数量有默认值
//   return {
//     price: priceItem?.price ?? 0,
//     quantity: priceItem?.quantity ?? 1
//   };
// };
function convertListToTemp(list = []) {
  const result = [];
  function generateCombinations(index, currentCombination) {
    if (index === list.length) {
      let priceAndQu;

      // 尝试从 priceInfo 中直接找是否有已存在的 optionCode
      const codeList = list.map((item, i) => {
        const child = item.children?.find(
          c => c.name === currentCombination[item.name]
        );
        return child?.id || "";
      });

      const optionCode = codeList.join("-");
      const matched = priceInfo.value.find(it => it.optionCode === optionCode);

      if (matched) {
        priceAndQu = {
          price: matched.price,
          quantity: matched.quantity
        };
      } else {
        priceAndQu = {
          price: 0,
          quantity: 1
        };
      }

      result.push({ ...currentCombination, ...priceAndQu });
      return;
    }

    const item = list[index];
    if (item.children && item.children.length > 0) {
      item.children.forEach(child => {
        currentCombination[item.name] = child.name;
        generateCombinations(index + 1, { ...currentCombination });
      });
    } else {
      currentCombination[item.name] = null;
      generateCombinations(index + 1, { ...currentCombination });
    }
  }
  generateCombinations(0, {});
  return result;
}
const getPriceAndQuantity = itemVal => {
  const valueCombination = Object.values(itemVal);

  // 如果是空组合（如初始化）
  if (valueCombination.every(v => v === null)) {
    return {
      price: 0,
      quantity: 1
    };
  }

  // 构造 optionCode
  const codeList = [];

  valueCombination.forEach((name, index) => {
    const specGroup = listVal.value.filter(item => item.name === name);

    if (specGroup.length > 0) {
      codeList.push(specGroup[0].id);
    }
  });

  const optionCode = codeList.join("-");
  const priceItem = priceInfo.value.find(it => it.optionCode === optionCode);

  return {
    price: priceItem?.price ?? 0,
    quantity: priceItem?.quantity ?? 1
  };
};
// 删除
function remove(index) {
  list.value.splice(index, 1);
}

// 删除单个
const remove1 = (item1, it, index1) => {
  list.value.forEach(item => {
    if (item.id === item1.id) {
      item.children.splice(index1, 1);
    }
  });
};

// 还原
const restoreEvt = () => {
  ElMessageBox.confirm("是否还原？", "还原", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: ""
  }).then(() => {
    initData();
  });
};
const submitLoading = ref(false);
const saveLoading = ref(false);
const saveBackLoading = ref(false);

// 统一的保留字检查函数
const validateReservedWords = () => {
  // 检查规格名称重复和保留字
  const specNames = list.value
    .map(item => item.name)
    .filter(name => name && name.trim() !== "");

  // 检查是否有使用保留字"价格"或"数量"
  const reservedNames = specNames.filter(
    name => name === "价格" || name === "数量"
  );
  if (reservedNames.length > 0) {
    ElMessage.warning(
      `规格名称"${reservedNames[0]}"为系统保留字，请修改后重试`
    );
    return false;
  }

  // 检查名称重复
  const duplicateSpecs = specNames.filter(
    (name, index) => specNames.indexOf(name) !== index
  );
  if (duplicateSpecs.length > 0) {
    ElMessage.warning(`规格名称"${duplicateSpecs[0]}"重复，请修改后重试`);
    return false;
  }

  // 检查每个规格下的条目名称重复
  for (const spec of list.value) {
    if (!spec.children) continue;
    const itemNames = spec.children.map(child => child.name);
    const duplicateItems = itemNames.filter(
      (name, index) => itemNames.indexOf(name) !== index
    );
    if (duplicateItems.length > 0) {
      ElMessage.warning(
        `规格"${spec.name}"下的条目"${duplicateItems[0]}"重复，请修改后重试`
      );
      return false;
    }
  }

  return true;
};

// 促销规则校验
const validatePromotionRules = () => {
  if (priceSettings.value.promotionEnabled) {
    if (
      !priceSettings.value.ruleFirstNEnabled &&
      !priceSettings.value.ruleTimeEnabled
    ) {
      ElMessage.warning("请选择促销规则");
      return false;
    }

    if (priceSettings.value.ruleFirstNEnabled) {
      if (
        !priceSettings.value.ruleFirstNCount ||
        Number(priceSettings.value.ruleFirstNCount) <= 0
      ) {
        ElMessage.warning("请填写有效的用户数量");
        return false;
      }
    }

    if (priceSettings.value.ruleTimeEnabled) {
      if (
        !priceSettings.value.ruleTimeRange ||
        priceSettings.value.ruleTimeRange.length !== 2 ||
        !priceSettings.value.ruleTimeRange[0] ||
        !priceSettings.value.ruleTimeRange[1]
      ) {
        ElMessage.warning("请选择有效的促销时间范围");
        return false;
      }
    }
  }
  return true;
};

// 保存并返回编辑
const submitBackForm = async val => {
  if (saveBackLoading.value) return;
  saveBackLoading.value = true;

  // 检查保留字，如果检查失败则停止loading并返回
  if (!validateReservedWords() || !validatePromotionRules()) {
    saveBackLoading.value = false;
    return;
  }

  await saveApi(val);
  saveBackLoading.value = false;
};
// 保存编辑
const saveForm = async val => {
  if (saveLoading.value) return;
  saveLoading.value = true;

  // 检查保留字，如果检查失败则停止loading并返回
  if (!validateReservedWords() || !validatePromotionRules()) {
    saveLoading.value = false;
    return;
  }

  await saveApi(val);
  saveLoading.value = false;
};
// 保存接口编辑页
const saveApi = async val => {
  if (submitLoading.value) return;

  // 使用统一的保留字检查函数
  if (!validateReservedWords() || !validatePromotionRules()) {
    return;
  }

  submitLoading.value = true;

  // 构建新的参数格式
  const coursePeriodFeeItems = [];

  // 构造促销信息（仅在开启时生成）
  const buildPromotion = () => {
    if (!priceSettings.value.promotionEnabled) return null;
    if (
      priceSettings.value.promotionPrice === "" ||
      priceSettings.value.promotionPrice === null ||
      priceSettings.value.promotionPrice === undefined
    )
      return null;
    const promotion = {
      promotionPrice: Number(priceSettings.value.promotionPrice)
    };
    // 若促销价高于课时费用，则不附加，防御性处理
    if (
      Number(priceSettings.value.promotionPrice) >
      Number(priceSettings.value.coursePrice)
    ) {
      return null;
    }
    if (
      priceSettings.value.ruleTimeEnabled &&
      Array.isArray(priceSettings.value.ruleTimeRange) &&
      priceSettings.value.ruleTimeRange.length === 2
    ) {
      const [start, end] = priceSettings.value.ruleTimeRange;
      promotion.startTime = dayjs(start).valueOf();
      promotion.endTime = dayjs(end).valueOf();
    }
    if (priceSettings.value.ruleFirstNEnabled) {
      if (
        priceSettings.value.ruleFirstNCount !== "" &&
        priceSettings.value.ruleFirstNCount !== null &&
        priceSettings.value.ruleFirstNCount !== undefined
      ) {
        promotion.totalQuota = Number(priceSettings.value.ruleFirstNCount);
      }
    }
    return promotion;
  };

  // 添加课程费用（并附带促销信息）
  if (
    priceSettings.value.coursePrice !== "" &&
    priceSettings.value.coursePrice !== null &&
    priceSettings.value.coursePrice !== undefined
  ) {
    const classHourItem = {
      feeType: "CLASS_HOUR",
      amount: Number(priceSettings.value.coursePrice) || 0,
      mandatory: true // 课程费用默认为必选
    };
    const promotion = buildPromotion();
    if (promotion) classHourItem.draftPromotion = promotion;
    coursePeriodFeeItems.push(classHourItem);
  }

  // 添加材料费用
  if (
    priceSettings.value.materialPrice !== "" &&
    priceSettings.value.materialPrice !== null &&
    priceSettings.value.materialPrice !== undefined
  ) {
    coursePeriodFeeItems.push({
      feeType: "MATERIAL",
      amount: Number(priceSettings.value.materialPrice) || 0,
      mandatory: priceSettings.value.priceType === "fixed" // 根据家长是否必选材料费用决定
    });
  }

  const params = removeEmptyValues({
    coursePeriodId: Number(route.query.periodId),
    feeItems: coursePeriodFeeItems
  });
  if (textareaRefund.value) {
    params.refundPolicy = textareaRefund.value;
  }
  if (textareaFree.value) {
    params.feeDescription = textareaFree.value;
  }
  // return;
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了"${props.periodName}"课期中的价格设置`
  };
  const [err, result] = await to(updatePriceSetting(params, operateLog));
  if (result?.code === 200) {
    // 如果是课程详情编辑页面，保存优惠券设置
    if (route.query.type === 'edite') {
      await saveCouponSettings();
    }

    ElMessage.success("保存成功");
    if (val) {
      if (route.query.fromPage === "courseDetail") {
        router.replace({
          path: "/course/courseDetails",
          query: { id: route.query.courseId }
        });
      } else if (route.query.fromPage === "currentDetail") {
        router.replace({
          path: "/course/courseDetails/currentDetails",
          query: {
            periodId: route.query.periodId,
            courseId: route.query.courseId
          }
        });
      }
    }
  } else {
    ElMessage.error(`保存失败,${result?.msg}`);
  }
  submitLoading.value = false;
};

const saveDraftApi = async () => {
  // 使用统一的保留字检查函数
  if (!validateReservedWords() || !validatePromotionRules()) {
    return;
  }

  // 构建新的参数格式
  const coursePeriodFeeItems = [];

  // 构造促销信息（仅在开启时生成）
  const buildDraftPromotion = () => {
    if (!priceSettings.value.promotionEnabled) return null;
    if (
      priceSettings.value.promotionPrice === "" ||
      priceSettings.value.promotionPrice === null ||
      priceSettings.value.promotionPrice === undefined
    )
      return null;
    const draftPromotion = {
      promotionPrice: Number(priceSettings.value.promotionPrice)
    };
    // 若促销价高于课时费用，则不附加，防御性处理
    if (
      Number(priceSettings.value.promotionPrice) >
      Number(priceSettings.value.coursePrice)
    ) {
      return null;
    }
    if (
      priceSettings.value.ruleTimeEnabled &&
      Array.isArray(priceSettings.value.ruleTimeRange) &&
      priceSettings.value.ruleTimeRange.length === 2
    ) {
      const [start, end] = priceSettings.value.ruleTimeRange;
      draftPromotion.startTime = dayjs(start).valueOf();
      draftPromotion.endTime = dayjs(end).valueOf();
    }
    if (priceSettings.value.ruleFirstNEnabled) {
      if (
        priceSettings.value.ruleFirstNCount !== "" &&
        priceSettings.value.ruleFirstNCount !== null &&
        priceSettings.value.ruleFirstNCount !== undefined
      ) {
        draftPromotion.totalQuota = Number(priceSettings.value.ruleFirstNCount);
      }
    }
    return draftPromotion;
  };

  // 添加课程费用（并附带促销信息）
  if (
    priceSettings.value.coursePrice !== "" &&
    priceSettings.value.coursePrice !== null &&
    priceSettings.value.coursePrice !== undefined
  ) {
    const classHourItem = {
      feeType: "CLASS_HOUR",
      amount: Number(priceSettings.value.coursePrice) || 0,
      mandatory: true
    };
    const dp = buildDraftPromotion();
    if (dp) classHourItem.draftPromotion = dp;
    coursePeriodFeeItems.push(classHourItem);
  }

  // 添加材料费用
  if (
    priceSettings.value.materialPrice !== "" &&
    priceSettings.value.materialPrice !== null &&
    priceSettings.value.materialPrice !== undefined
  ) {
    coursePeriodFeeItems.push({
      feeType: "MATERIAL",
      amount: Number(priceSettings.value.materialPrice) || 0,
      mandatory: priceSettings.value.priceType === "fixed" // 根据家长是否必选材料费用决定
    });
  }

  const params = removeEmptyValues({
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    coursePeriodFeeItems
  });
  if (textareaRefund.value) {
    params.refundPolicy = textareaRefund.value;
  }
  if (textareaFree.value) {
    params.feeDescription = textareaFree.value;
  }
  // console.log("🎁params------------------------------>", params);
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${props.periodName}”课期中的价格设置保存在草稿箱`
  };
  const [err, result] = await to(saveDraftPriceSetting(params, operateLog));
  if (result?.code === 200) {
    ElMessage.success("当前资料已保存到草稿箱");
  } else {
    ElMessage.error(`保存失败,${result?.msg}`);
  }
};
// 保存草稿箱
const drafLoading = ref(false);
const submitDraftForm = async () => {
  if (drafLoading.value) return;
  drafLoading.value = true;

  // 检查保留字，如果检查失败则停止loading并返回
  if (!validateReservedWords() || !validatePromotionRules()) {
    drafLoading.value = false;
    return;
  }

  await saveDraftApi();
  drafLoading.value = false;
};
// 下一步草稿箱
const nextSubmitLoading = ref(false);
const nextSubmitForm = async () => {
  // 验证是否设置了价格
  if (!priceSettings.value.coursePrice && !priceSettings.value.materialPrice) {
    ElMessage.error("请设置课程费用或材料费用");
    return;
  }
  // 若开启促销，需校验促销价有效
  if (
    priceSettings.value.promotionEnabled &&
    (!priceSettings.value.promotionPrice ||
      Number(priceSettings.value.promotionPrice) <= 0)
  ) {
    ElMessage.error("请填写有效的促销价");
    return;
  }
  if (nextSubmitLoading.value) return;
  nextSubmitLoading.value = true;

  // 检查保留字，如果检查失败则不继续执行
  if (!validateReservedWords() || !validatePromotionRules()) {
    nextSubmitLoading.value = false;
    return;
  }

  await saveDraftApi();
  nextSubmitLoading.value = false;
  emites("baseInfo", {
    infoShow: "完成",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    complete: true
  });
};
// 上一步草稿箱
const lastSubmitLoading = ref(false);
const lastSubmitForm = async () => {
  if (lastSubmitLoading.value) return;
  lastSubmitLoading.value = true;

  // 检查保留字，如果检查失败则不继续执行
  if (!validateReservedWords() || !validatePromotionRules()) {
    lastSubmitLoading.value = false;
    return;
  }

  await saveDraftApi();
  lastSubmitLoading.value = false;
  emites("baseInfo", {
    infoShow: "用户协议",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  });
};
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        // 检查保留字和促销规则，如果检查失败则不执行保存和跳转
        if (!validateReservedWords() || !validatePromotionRules()) {
          return;
        }
        saveDraftApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};
const priceInfo = ref([]);
const listVal = ref([]);
const initData = async () => {
  // 页面初始化：查询课期草稿统计（按草稿ID）
  if (route.query.draftId || useCourseStore.draftId) {
    const [errCount, countRes] = await requestTo(
      draftCoursePeriodCountByDraftId({
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
      })
    );
    const draftCount = countRes?.count ?? 0;
    timeRuleDisabled.value = draftCount > 1;
    if (timeRuleDisabled.value) {
      // 禁用时强制关闭并清空该规则
      priceSettings.value.ruleTimeEnabled = false;
      priceSettings.value.ruleTimeRange = [];
    }
  }

  if (isEdit.value) {
    await getFreeData();
    const periodId = route.query.periodId;
    const [err, result] = await requestTo(
      //  findSpecificationByCoursePeriodId({
      findFeeItemByCoursePeriodId({
        coursePeriodId: periodId
      })
    );
    // const [err2, result2] = await requestTo(
    //   findSpecificationByCoursePeriodIdPrice({
    //     coursePeriodId: periodId
    //   })
    // );
    // console.log("🎁-----result2-----", result2);
    // console.log("🎁-----result-----", result);
    // priceInfo.value = result2 || [];
    // console.log(
    //   "🐠 priceInfo.value------------------------------>",
    //   priceInfo.value
    // );

    // 处理 findFeeItemByCoursePeriodId 返回的价格数据
    if (result && Array.isArray(result)) {
      result.forEach(item => {
        if (item.feeType === "CLASS_HOUR") {
          priceSettings.value.coursePrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";

          // 处理促销价数据回显
          if (item.promotion) {
            // 有促销价数据，设置是否开启促销价为是
            priceSettings.value.promotionEnabled = true;

            // 回显促销价
            priceSettings.value.promotionPrice =
              item.promotion.promotionPrice !== undefined &&
              item.promotion.promotionPrice !== null
                ? item.promotion.promotionPrice
                : "";

            // 回显促销规则：前N名用户
            if (item.promotion.totalQuota) {
              priceSettings.value.ruleFirstNEnabled = true;
              priceSettings.value.ruleFirstNCount = item.promotion.totalQuota;
            }

            // 回显促销规则：时间范围
            if (item.promotion.startTime && item.promotion.endTime) {
              priceSettings.value.ruleTimeEnabled = true;
              // 将时间戳转换为日期时间格式
              const startTime = dayjs(item.promotion.startTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              const endTime = dayjs(item.promotion.endTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              priceSettings.value.ruleTimeRange = [startTime, endTime];
            }
          } else {
            // 没有促销价数据，重置促销价相关字段
            priceSettings.value.promotionEnabled = false;
            priceSettings.value.promotionPrice = "";
            priceSettings.value.ruleFirstNEnabled = false;
            priceSettings.value.ruleFirstNCount = "";
            priceSettings.value.ruleTimeEnabled = false;
            priceSettings.value.ruleTimeRange = [];
          }
        } else if (item.feeType === "MATERIAL") {
          priceSettings.value.materialPrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";
          // 材料费用的mandatory决定priceType
          priceSettings.value.priceType = item.mandatory
            ? "fixed"
            : "negotiable";
        }
      });
    }

    setData(result || []);
  } else if (route.query.draftId || useCourseStore.draftId) {
    const [err, result] = await requestTo(
      //  findSpecificationByCoursePeriodId({
      findSpecificationNameOptionByDraftId({
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
      })
    );
    // const [err2, result2] = await requestTo(
    //   findSpecificationTableByDraftId({
    //     draftId: Number(route.query.draftId) || props.draftId
    //   })
    // );
    const [err2, result2] = await requestTo(
      findSpecificationByDraftId({
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
      })
    );
    const [err3, result3] = await requestTo(
      priceSettingFindByDraftId({
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
      })
    );
    const [err4, result4] = await requestTo(
      findFeeItemByDraftId({
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
      })
    );
    // console.log("🎁-----result2--ccc---", result2);
    // console.log("🎁-----result--ccc---", result);
    // console.log("🎁-----result3---ccc--", result3);
    // console.log("🎁-----result4---ccc--", result4);
    textareaRefund.value = result3?.refundPolicy || "";
    textareaFree.value = result3?.feeDescription || "";

    // 解析价格设置数据 - 使用新的接口数据
    if (result4 && Array.isArray(result4)) {
      result4.forEach(item => {
        if (item.feeType === "CLASS_HOUR") {
          priceSettings.value.coursePrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";

          // 处理促销价数据回显
          if (item.promotion) {
            // 有促销价数据，设置是否开启促销价为是
            priceSettings.value.promotionEnabled = true;

            // 回显促销价
            priceSettings.value.promotionPrice =
              item.promotion.promotionPrice !== undefined &&
              item.promotion.promotionPrice !== null
                ? item.promotion.promotionPrice
                : "";

            // 回显促销规则：前N名用户
            if (item.promotion.totalQuota) {
              priceSettings.value.ruleFirstNEnabled = true;
              priceSettings.value.ruleFirstNCount = item.promotion.totalQuota;
            }

            // 回显促销规则：时间范围
            if (item.promotion.startTime && item.promotion.endTime) {
              priceSettings.value.ruleTimeEnabled = true;
              // 将时间戳转换为日期时间格式
              const startTime = dayjs(item.promotion.startTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              const endTime = dayjs(item.promotion.endTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              priceSettings.value.ruleTimeRange = [startTime, endTime];
            }
          } else {
            // 没有促销价数据，重置促销价相关字段
            priceSettings.value.promotionEnabled = false;
            priceSettings.value.promotionPrice = "";
            priceSettings.value.ruleFirstNEnabled = false;
            priceSettings.value.ruleFirstNCount = "";
            priceSettings.value.ruleTimeEnabled = false;
            priceSettings.value.ruleTimeRange = [];
          }
        } else if (item.feeType === "MATERIAL") {
          priceSettings.value.materialPrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";
          // 材料费用的mandatory决定priceType
          priceSettings.value.priceType = item.mandatory
            ? "fixed"
            : "negotiable";
        }
      });
    }

    priceInfo.value = result2 || [];
    setData(result || [], result2 || []);
    textareaFree.value = freeValue;
    textareaRefund.value = refundValue;
  }

  listVal.value = getValList();
  temcolumns.value = getColumsData();
  temData.value = convertListToTemp(list.value);

  // 初始化优惠券数据（仅在课程详情编辑页面）
  if (route.query.type === 'edite') {
    await getSelectedCoupons();
    await getCouponList();
  }
};
const isEdit = ref(false);

// 监听窗口大小变化，调整表格尺寸
const updateTableSize = () => {
  const container = document.querySelector(".table-add");
  if (container) {
    tableWidth.value = container.clientWidth;
    tableHeight.value = container.clientHeight;
  }
};
// 获取费用说明及退款信息
const getFreeLoading = ref(false);
const getFreeData = async data => {
  if (getFreeLoading.value) {
    return;
  }
  getFreeLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  const [err, result] = await to(CoursePeriodIdByFree(paramsData));
  if (result?.code === 200) {
    textareaFree.value = result?.data?.feeDescription || "";
    textareaRefund.value = result?.data?.refundPolicy || "";

    // 解析价格设置数据
    if (
      result?.data?.coursePeriodFeeItems &&
      Array.isArray(result.data.coursePeriodFeeItems)
    ) {
      result.data.coursePeriodFeeItems.forEach(item => {
        if (item.feeType === "CLASS_HOUR") {
          priceSettings.value.coursePrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";

          // 处理促销价数据回显
          if (item.draftPromotion) {
            // 有促销价数据，设置是否开启促销价为是
            priceSettings.value.promotionEnabled = true;

            // 回显促销价
            priceSettings.value.promotionPrice =
              item.draftPromotion.promotionPrice !== undefined &&
              item.draftPromotion.promotionPrice !== null
                ? item.draftPromotion.promotionPrice
                : "";

            // 回显促销规则：前N名用户
            if (item.draftPromotion.totalQuota) {
              priceSettings.value.ruleFirstNEnabled = true;
              priceSettings.value.ruleFirstNCount =
                item.draftPromotion.totalQuota;
            }

            // 回显促销规则：时间范围
            if (item.draftPromotion.startTime && item.draftPromotion.endTime) {
              priceSettings.value.ruleTimeEnabled = true;
              // 将时间戳转换为日期时间格式
              const startTime = dayjs(item.draftPromotion.startTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              const endTime = dayjs(item.draftPromotion.endTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              priceSettings.value.ruleTimeRange = [startTime, endTime];
            }
          } else {
            // 没有促销价数据，重置促销价相关字段
            priceSettings.value.promotionEnabled = false;
            priceSettings.value.promotionPrice = "";
            priceSettings.value.ruleFirstNEnabled = false;
            priceSettings.value.ruleFirstNCount = "";
            priceSettings.value.ruleTimeEnabled = false;
            priceSettings.value.ruleTimeRange = [];
          }
        } else if (item.feeType === "MATERIAL") {
          priceSettings.value.materialPrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";
          // 材料费用的mandatory决定priceType
          priceSettings.value.priceType = item.mandatory
            ? "fixed"
            : "negotiable";
        }
      });
    }
  } else {
    // ElMessage.error('获取失败');
    console.log("无数据");
  }
  getFreeLoading.value = false;
};

// 优惠券相关方法
// 获取优惠券列表
const getCouponList = async (page = 0) => {
  if (couponData.value.loading) return;

  couponData.value.loading = true;
  try {
    const params = {
      timeStamp: Date.now(),
      page,
      size: couponData.value.pagination.size,
      couponScope: "LIMIT"
    };

    const [err, result] = await to(getCouponFindAll(params));
    if (result?.code === 200 && result.data) {
      // 处理优惠券数据，添加显示文本
      const processedList = result.data.content.map(item => ({
        ...item,
        couponTypeText: getCouponTypeText(item.couponType, item.couponDiscountType, item.discountAmount, item.conditionAmount),
        feeTypeText: getFeeTypeText(item.feeType),
        distributionTime: formatCouponTime(item.distributionStartTime, item.distributionEndTime),
        useTime: formatCouponTime(item.startTime, item.endTime)
      }));

      couponData.value.list = processedList;
      couponData.value.pagination = {
        page: result.data.number,
        size: result.data.size,
        total: result.data.totalElements,
        totalPages: result.data.totalPages
      };

      // 更新选中状态
      updateSelectionStatus();
    }
  } catch (error) {
    console.error("获取优惠券列表失败:", error);
    ElMessage.error("获取优惠券列表失败");
  } finally {
    couponData.value.loading = false;
  }
};

// 获取已选中的优惠券
const getSelectedCoupons = async () => {
  if (!route.query.periodId) return;

  try {
    const [err, result] = await to(findCouponByCoursePeriodId({
      coursePeriodId: route.query.periodId
    }));

    if (result?.code === 200 && result.data) {
      couponData.value.selectedIds = result.data.map(item => item.id);
      couponData.value.couponAvailable = result.data.length > 0;
    }
  } catch (error) {
    console.error("获取已选中优惠券失败:", error);
  }
};

// 更新选中状态
const updateSelectionStatus = () => {
  // 这里会在表格渲染后更新选中状态
  nextTick(() => {
    if (couponTableRef.value) {
      couponData.value.list.forEach(row => {
        if (couponData.value.selectedIds.includes(row.id)) {
          couponTableRef.value.toggleRowSelection(row, true);
        }
      });
    }
  });
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  // 获取当前页面所有优惠券的ID
  const currentPageIds = couponData.value.list.map(item => item.id);

  // 移除当前页面的所有ID
  couponData.value.selectedIds = couponData.value.selectedIds.filter(
    id => !currentPageIds.includes(id)
  );

  // 添加当前页面选中的ID
  const selectedIds = selection.map(item => item.id);
  couponData.value.selectedIds.push(...selectedIds);

  // 更新优惠券可用状态
  couponData.value.couponAvailable = couponData.value.selectedIds.length > 0;
};

// 分页变化
const handleCouponPageChange = (page) => {
  getCouponList(page - 1); // Element Plus分页从1开始，API从0开始
};

// 格式化优惠券类型文本
const getCouponTypeText = (couponType, discountType, discountAmount, conditionAmount) => {
  if (couponType === "FULL_REDUCTION") {
    return `满${conditionAmount}减${discountAmount}`;
  } else if (couponType === "DISCOUNT") {
    return `${discountAmount}折`;
  } else if (couponType === "FIXED_AMOUNT") {
    return `固定${discountAmount}元`;
  }
  return "未知";
};

// 格式化费用类型文本
const getFeeTypeText = (feeType) => {
  const feeTypeMap = {
    CLASS_HOUR: "课时",
    INSURANCE: "保险",
    MATERIAL: "材料",
    SERVICE: "服务"
  };
  return feeTypeMap[feeType] || "未知";
};

// 格式化时间范围
const formatCouponTime = (startTime, endTime) => {
  if (!startTime || !endTime) return "-";
  return `${dayjs(startTime).format("YYYY-MM-DD")} 至 ${dayjs(endTime).format("YYYY-MM-DD")}`;
};

// 保存优惠券设置
const saveCouponSettings = async () => {
  if (!route.query.periodId) return;

  try {
    const params = {
      coursePeriodId: Number(route.query.periodId),
      couponAvailable: couponData.value.couponAvailable,
      couponIds: couponData.value.selectedIds
    };

    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `更新了"${props.periodName}"课期的优惠券设置`
    };

    const [err, result] = await to(updateCoursePeriodCoupon(params, operateLog));
    if (result?.code !== 200) {
      console.error("保存优惠券设置失败:", result?.msg);
    }
  } catch (error) {
    console.error("保存优惠券设置失败:", error);
  }
};

const couponTableRef = ref();

onMounted(() => {
  isEdit.value = route.query.back === "value" || route.query.type === "edite";
  initData();
  nextTick(() => {
    updateTableSize();
    window.addEventListener("resize", updateTableSize);
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", updateTableSize);
});

// 修改列配置格式以适配 el-table-v2
const formatColumns = columns => {
  if (!columns || !Array.isArray(columns)) return [];
  return columns.map(col => ({
    key: col.prop,
    dataKey: col.prop,
    title: col.label,
    width: 150,
    ...col
  }));
};

// 监听list变化
watch(
  () => list.value,
  newVal => {
    handleUpdateData();
  },
  { deep: true }
);

const submitDraftFormatt = async () => {
  const tre = ref(false);
  drafLoading.value = true;

  // 使用统一的保留字检查函数
  if (!validateReservedWords()) {
    drafLoading.value = false;
    return false;
  }

  // 构建新的参数格式
  const coursePeriodFeeItems = [];

  // 构造促销信息（仅在开启时生成）
  const buildDraftPromotion = () => {
    if (!priceSettings.value.promotionEnabled) return null;
    if (
      priceSettings.value.promotionPrice === "" ||
      priceSettings.value.promotionPrice === null ||
      priceSettings.value.promotionPrice === undefined
    )
      return null;
    const draftPromotion = {
      promotionPrice: Number(priceSettings.value.promotionPrice)
    };
    if (
      priceSettings.value.ruleTimeEnabled &&
      Array.isArray(priceSettings.value.ruleTimeRange) &&
      priceSettings.value.ruleTimeRange.length === 2
    ) {
      const [start, end] = priceSettings.value.ruleTimeRange;
      draftPromotion.startTime = dayjs(start).valueOf();
      draftPromotion.endTime = dayjs(end).valueOf();
    }
    if (priceSettings.value.ruleFirstNEnabled) {
      if (
        priceSettings.value.ruleFirstNCount !== "" &&
        priceSettings.value.ruleFirstNCount !== null &&
        priceSettings.value.ruleFirstNCount !== undefined
      ) {
        draftPromotion.totalQuota = Number(priceSettings.value.ruleFirstNCount);
      }
    }
    return draftPromotion;
  };

  // 添加课程费用（并附带促销信息）
  if (
    priceSettings.value.coursePrice !== "" &&
    priceSettings.value.coursePrice !== null &&
    priceSettings.value.coursePrice !== undefined
  ) {
    const classHourItem = {
      feeType: "CLASS_HOUR",
      amount: Number(priceSettings.value.coursePrice) || 0,
      mandatory: true
    };
    const dp = buildDraftPromotion();
    if (dp) classHourItem.draftPromotion = dp;
    coursePeriodFeeItems.push(classHourItem);
  }

  // 添加材料费用
  if (
    priceSettings.value.materialPrice !== "" &&
    priceSettings.value.materialPrice !== null &&
    priceSettings.value.materialPrice !== undefined
  ) {
    coursePeriodFeeItems.push({
      feeType: "MATERIAL",
      amount: Number(priceSettings.value.materialPrice) || 0,
      mandatory: priceSettings.value.priceType === "fixed" // 根据家长是否必选材料费用决定
    });
  }

  const params = removeEmptyValues({
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    coursePeriodFeeItems
  });
  if (textareaRefund.value) {
    params.refundPolicy = textareaRefund.value;
  }
  if (textareaFree.value) {
    params.feeDescription = textareaFree.value;
  }
  // console.log("🎁params------------------------------>", params);
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${props.periodName}”课期中的价格设置保存在草稿箱`
  };
  const [err, result] = await to(saveDraftPriceSetting(params, operateLog));
  if (result?.code === 200) {
    ElMessage.success("当前资料已保存到草稿箱");
    tre.value = true;
  } else {
    ElMessage.error(`保存失败,${result?.msg}`);
    tre.value = false;
  }
  drafLoading.value = false;
  return tre.value;
};
// 用于草稿箱点击价格设置校验
defineExpose({
  submitDraftFormatt
});
</script>

<template>
  <!-- <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    /> -->
  <div class="price-edite">
    <div class="price-container">
      <!-- 规格相关按钮 - 已移除 -->
      <!-- <div class="buttons">
        <div class="left">
          <el-button type="primary" @click="addEvt">
            {{ "添加规格" }}
          </el-button>
        </div>
        <div class="left">
          <el-button
            @click="
              router.replace({
                path: '/course/courseDetails/currentDetails',
                query: { infoShow: '价格设置', periodId: route.query.periodId }
              })
            "
          >
            取消
          </el-button>
          <el-button type="danger" @click="restoreEvt">
            {{ "还原" }}
          </el-button>
          <el-button type="primary" :loading="submitLoading" @click="saveEvt">
            {{ "保存" }}
          </el-button>
        </div>
      </div> -->

      <!-- 规格编辑区域 - 已移除 -->
      <!-- <div class="content-add">
        <div class="flex">
          <VueDraggable
            v-model="list"
            :animation="150"
            handle=".handle"
            class="flex flex-col w-full"
          >
            <div v-for="(item, index) in list" :key="item.id" class="spec-item">
              <div class="spec-header">
                <el-icon class="handle"><Sort /></el-icon>
                <el-input
                  v-model.trim="item.name"
                  class="spec-input"
                  placeholder="规格名称"
                  maxlength="30"
                  show-word-limit
                />
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click="remove(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  class="add-row-btn"
                  @click="addRowEvt(item, item.children)"
                >
                  <el-icon><Plus /></el-icon>添加条目
                </el-button>
              </div>

              <div class="child-list">
                <VueDraggable
                  v-model="item.children"
                  :animation="150"
                  handle=".handle"
                  class="child-items"
                >
                  <div
                    v-for="(it, index) in item.children"
                    :key="it.id"
                    class="child-item"
                  >
                    <el-input
                      v-model.trim="it.name"
                      class="child-input"
                      size="small"
                      placeholder="条目名称"
                      maxlength="10"
                      show-word-limit
                    />

                    <div class="child-actions">
                      <el-icon class="handle"><Rank /></el-icon>
                      <el-button
                        type="danger"
                        size="small"
                        circle
                        @click="remove1(item, it, index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </VueDraggable>
              </div>
            </div>
          </VueDraggable>
        </div>
      </div> -->
      <!-- 规格表格展示区域 - 已移除 -->
      <!-- <div
        class="table-add"
        :style="{ maxHeight: `${MAX_TABLE_HEIGHT}px`, overflowY: 'auto' }"
      >
        <el-table-v2
          v-if="list.some(item => item.children && item.children.length > 0)"
          ref="tableRef"
          :columns="temcolumns"
          :data="temData"
          :width="tableWidth"
          :height="tableHeight"
          fixed
          :loading="loadingTable"
        />
      </div> -->

      <!-- 新的价格设置 -->
      <div class="price-setting-section">
        <div class="title">价格设置</div>
        <div class="price-form">
          <!-- 课程费用 -->
          <div class="form-item">
            <label class="form-label">课时费用</label>
            <div class="input-with-unit">
              <el-input
                v-model="priceSettings.coursePrice"
                type="text"
                placeholder="请输入课程费用"
                class="price-input"
                @input="handleCoursePrice"
                @blur="formatMoneyOnBlur('coursePrice')"
              />
              <span class="unit">元</span>
            </div>
          </div>

          <!-- 是否开启促销价 -->
          <div class="form-item">
            <label class="form-label">是否开启促销价</label>
            <div class="radio-group">
              <el-radio-group v-model="priceSettings.promotionEnabled">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </div>
          </div>

          <!-- 开启促销后展示：促销价与促销规则 -->
          <template v-if="priceSettings.promotionEnabled">
            <!-- 促销价 -->
            <div class="form-item">
              <label class="form-label">促销价</label>
              <div class="input-with-unit">
                <el-input
                  v-model="priceSettings.promotionPrice"
                  type="text"
                  placeholder="请输入促销价"
                  class="price-input"
                  @input="handlePromotionPrice"
                  @blur="formatMoneyOnBlur('promotionPrice')"
                />
                <span class="unit">元</span>
              </div>
            </div>

            <!-- 促销规则设置 -->
            <div class="form-item promotion-block">
              <div class="sub-title">促销规则设置</div>
              <div class="promotion-row">
                <el-checkbox v-model="priceSettings.ruleFirstNEnabled">
                  前
                  <el-input
                    v-model.number="priceSettings.ruleFirstNCount"
                    type="number"
                    min="1"
                    step="1"
                    style="width: 120px; margin: 0 6px"
                    placeholder="人数"
                    @input="
                      priceSettings.ruleFirstNCount =
                        priceSettings.ruleFirstNCount
                          .toString()
                          .replace(/[^\d]/g, '')
                    "
                  />
                  名用户可以享受促销价；
                </el-checkbox>
              </div>
              <div class="promotion-row">
                <el-checkbox
                  v-model="priceSettings.ruleTimeEnabled"
                  :disabled="timeRuleDisabled"
                  :class="{ 'disabled-red-text': timeRuleDisabled }"
                >
                  <el-date-picker
                    v-model="priceSettings.ruleTimeRange"
                    type="datetimerange"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    range-separator="-"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled="timeRuleDisabled"
                  />
                  <span style="margin-left: 6px">前报名用户可以享受促销价；</span>
                </el-checkbox>
                <el-tooltip
                  v-if="timeRuleDisabled"
                  class="box-item"
                  effect="light"
                  placement="bottom"
                  content="因涉及多个开课时间，无法统一确定优惠时段。请创建后前往课期编辑页面，为各开课时间单独设置促销规则。"
                >
                  <el-icon style="color: red; margin-left: 3px">
                    <Warning />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>

          <!-- 材料费用 -->
          <div class="form-item">
            <label class="form-label">材料费用</label>
            <div class="input-with-unit">
              <el-input
                v-model="priceSettings.materialPrice"
                type="text"
                placeholder="请输入材料费用"
                class="price-input"
                @input="handleMaterialPrice"
                @blur="formatMoneyOnBlur('materialPrice')"
              />
              <span class="unit">元</span>
            </div>
          </div>

          <!-- 价格类型选择 -->
          <div class="form-item">
            <label class="form-label">家长是否必选材料费用</label>
            <div class="radio-group">
              <el-radio-group v-model="priceSettings.priceType">
                <el-radio value="fixed">是</el-radio>
                <el-radio value="negotiable">否</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 优惠券管理（仅在课程详情编辑页面显示） -->
      <div v-if="route.query.type === 'edite'" class="coupon-section">
        <div class="title">设置可用优惠券</div>
        <div class="coupon-table-container">
          <el-table
            ref="couponTableRef"
            :data="couponData.list"
            :loading="couponData.loading"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="name"
              label="优惠券名称"
              width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="couponTypeText"
              label="优惠券类型"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="feeTypeText"
              label="优惠费用类型"
              width="120"
            />
            <el-table-column
              prop="distributionTime"
              label="发放时间"
              width="180"
              show-overflow-tooltip
            />
            <el-table-column
              prop="useTime"
              label="使用时间"
              width="180"
              show-overflow-tooltip
            />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container" style="margin-top: 16px; text-align: right;">
            <el-pagination
              :current-page="couponData.pagination.page + 1"
              :page-size="couponData.pagination.size"
              :total="couponData.pagination.total"
              layout="total, prev, pager, next, jumper"
              @current-change="handleCouponPageChange"
            />
          </div>
        </div>
      </div>

      <!-- 费用说明（包含、不包含） -->
      <div class="free-add">
        <div class="title">{{ "费用说明（包含、不包含）" }}</div>
        <div class="text-area">
          <el-input
            v-model="textareaFree"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入"
            resize="none"
          />
        </div>
      </div>
      <!-- 退款说明 -->
      <div class="free-add">
        <div class="title">{{ "退款政策" }}</div>
        <div class="text-area">
          <el-input
            v-model="textareaRefund"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入"
            resize="none"
          />
        </div>
      </div>
    </div>
    <div class="buttons-bottom">
      <div v-if="route.query.type === 'edite'" class="left">
        <el-button class="cancel" @click="backEvt('back')"> 返回 </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="saveBackLoading"
          @click="submitBackForm(true)"
        >
          {{ "保存并返回" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="saveLoading"
          @click="saveForm(false)"
        >
          {{ "保存" }}
        </el-button>
      </div>
      <div v-else class="left">
        <!-- <el-button class="cancel" @click="backEvt('exit')"> 退出 </el-button> -->
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          class="create"
          :loading="drafLoading"
          @click="submitDraftForm()"
        >
          {{ "保存草稿箱" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="lastSubmitLoading"
          @click="lastSubmitForm(ruleFormRef)"
        >
          {{ "上一步" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="nextSubmitLoading"
          @click="nextSubmitForm(ruleFormRef)"
        >
          {{ "下一步" }}
        </el-button>
      </div>
      <div class="right">
        <el-button
          type="primary"
          class="create"
          @click="aiNewPage(infoShowEnName)"
        >
          {{ "AI课程设计" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.price-edite {
  box-sizing: border-box;
  width: 100%;
  // height: auto;
  // max-height: 600px;
  height: 100%;
  // padding: 20px 20px;
  padding: 10px 0 0 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  .price-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
  }
  .buttons {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .left {
      display: flex;
    }

    .cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(230 152 58 / 100%);
      border-radius: 6px;
      margin-left: 58px;
      margin-right: 10px;
    }

    .create {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(64 149 229 / 100%);
      border-radius: 6px;
      margin-right: 10px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgb(45, 130, 210);
      }
    }

    .create:last-child {
      margin-right: 0;
    }
  }

  .content-add {
    width: 100%;
    height: 230px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    padding: 20px;
    box-sizing: border-box;
    transition: all 0.3s ease;
    background-color: #f9fafb;

    &:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    .spec-item {
      background-color: white;
      border-radius: 8px;
      padding: 8px 12px;
      margin-bottom: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid #ebeef5;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .spec-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .handle {
          cursor: move;
          font-size: 16px;
          color: #909399;
          margin-right: 8px;
        }

        .spec-input {
          width: 300px;
          margin-right: 8px;
        }
      }

      .child-list {
        margin: 4px 0;
        padding-left: 16px;

        .child-items {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .child-item {
          display: flex;
          align-items: center;
          background-color: #f5f7fa;
          border-radius: 6px;
          padding: 4px 8px;
          min-width: 160px;

          .child-input {
            width: 180px;
            margin-right: 8px;
          }

          .child-actions {
            display: flex;
            align-items: center;
            gap: 6px;

            .handle {
              cursor: move;
              color: #909399;
              font-size: 14px;
            }
          }
        }
      }

      .add-row-btn {
        padding: 4px 8px;
        height: 28px;
      }
    }
  }
  .buttons-bottom {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: space-between;
  }
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;

  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px #409eff inset;
  }
}
.table-add {
  // flex: 1;
  // height: 100%;
  // min-height: 300px;
  // position: relative;
  // max-height: 500px;
  // overflow-y: auto;
  // border: 1px solid #ebeef5;
  margin-bottom: 20px;
}
.title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}
// 新的价格设置样式
.price-setting-section {
  margin-bottom: 20px;

  .price-form {
    .form-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        font-size: 14px;
        color: #606266;
        margin-right: 15px;
        text-align: right;
      }

      .input-with-unit {
        display: flex;
        align-items: center;

        .price-input {
          width: 200px;
          margin-right: 8px;
        }

        .unit {
          font-size: 14px;
          color: #909399;
        }
      }

      .radio-group {
        display: flex;
        align-items: center;
      }

      &.promotion-block {
        flex-direction: column;
        align-items: flex-start;
        .sub-title {
          font-size: 14px;
          color: #606266;
          font-weight: 600;
          margin-bottom: 20px;
        }
        .promotion-row {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }
  }
}
.text-area {
  height: 220px;
  margin-bottom: 20px;
}
:deep(.el-textarea__inner) {
  height: 220px;
}
//滚动条的宽度
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #e4e4e4;
  border-radius: 3px;
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 3px;
}
:deep(.el-button:focus-visible) {
  display: none;
}
.disabled-red-text {
  background: #fff !important;
  span {
    color: #939393 !important;
  }
}

// 优惠券相关样式
.coupon-section {
  margin-bottom: 20px;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    padding-left: 8px;
    border-left: 4px solid #409eff;
  }

  .coupon-table-container {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e4e7ed;

    :deep(.el-table) {
      .el-table__header-wrapper {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 600;
        }
      }

      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
}
</style>
